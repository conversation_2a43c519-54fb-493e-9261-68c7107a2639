Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32l431xx.o(RESET) refers to startup_stm32l431xx.o(STACK) for __initial_sp
    startup_stm32l431xx.o(RESET) refers to startup_stm32l431xx.o(.text) for Reset_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.ADC1_IRQHandler) for ADC1_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.SDMMC1_IRQHandler) for SDMMC1_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it.o(i.LPUART1_IRQHandler) for LPUART1_IRQHandler
    startup_stm32l431xx.o(.text) refers to system_stm32l4xx.o(i.SystemInit) for SystemInit
    startup_stm32l431xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.HAL_UARTEx_RxEventCallback) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    main.o(i.HAL_UARTEx_RxEventCallback) refers to stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) for HAL_UARTEx_ReceiveToIdle_IT
    main.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for .bss
    main.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for hlpuart1
    main.o(i.PeriphCommonClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.PeriphCommonClock_Config) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableMSIPLLMode) for HAL_RCCEx_EnableMSIPLLMode
    main.o(i.esp8266_publish_ack) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.esp8266_publish_ack) refers to usart.o(i.uart_print) for uart_print
    main.o(i.esp8266_publish_ack) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.esp8266_publish_ack) refers to usart.o(.bss) for hlpuart1
    main.o(i.main) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to main.o(i.PeriphCommonClock_Config) for PeriphCommonClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_LPUART1_UART_Init) for MX_LPUART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to quadspi.o(i.MX_QUADSPI_Init) for MX_QUADSPI_Init
    main.o(i.main) refers to sdmmc.o(i.MX_SDMMC1_SD_Init) for MX_SDMMC1_SD_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C3_Init) for MX_I2C3_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to spi.o(i.MX_SPI2_Init) for MX_SPI2_Init
    main.o(i.main) refers to spi.o(i.MX_SPI3_Init) for MX_SPI3_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to dac.o(i.MX_DAC1_Init) for MX_DAC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM16_Init) for MX_TIM16_Init
    main.o(i.main) refers to lcd.o(i.LCD_Init) for LCD_Init
    main.o(i.main) refers to e53_ia1.o(i.E53_IA1_Init) for E53_IA1_Init
    main.o(i.main) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.main) refers to esp8266.o(i.esp8266_init) for esp8266_init
    main.o(i.main) refers to stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) for HAL_UARTEx_ReceiveToIdle_IT
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to e53_ia1.o(i.E53_IA1_Read_Data) for E53_IA1_Read_Data
    main.o(i.main) refers to esp8266.o(i.esp8266_publish) for esp8266_publish
    main.o(i.main) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.main) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to usart.o(.bss) for hlpuart1
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC1_Init) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.MX_DAC1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.MX_DAC1_Init) refers to stm32l4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC1_Init) refers to stm32l4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC1_Init) refers to dac.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    i2c.o(i.HAL_I2C_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C3_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C3_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C3_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C3_Init) refers to i2c.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_LPUART1_UART_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_LPUART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_LPUART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    usart.o(i.uart_print) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.uart_print) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart.o(i.uart_print) refers to strlen.o(.text) for strlen
    usart.o(i.uart_print) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    quadspi.o(i.HAL_QSPI_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    quadspi.o(i.HAL_QSPI_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    quadspi.o(i.HAL_QSPI_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    quadspi.o(i.MX_QUADSPI_Init) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_Init) for HAL_QSPI_Init
    quadspi.o(i.MX_QUADSPI_Init) refers to main.o(i.Error_Handler) for Error_Handler
    quadspi.o(i.MX_QUADSPI_Init) refers to quadspi.o(.bss) for .bss
    sdmmc.o(i.HAL_SD_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    sdmmc.o(i.HAL_SD_MspDeInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    sdmmc.o(i.HAL_SD_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    sdmmc.o(i.HAL_SD_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    sdmmc.o(i.HAL_SD_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    sdmmc.o(i.HAL_SD_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    sdmmc.o(i.MX_SDMMC1_SD_Init) refers to stm32l4xx_hal_sd.o(i.HAL_SD_Init) for HAL_SD_Init
    sdmmc.o(i.MX_SDMMC1_SD_Init) refers to sdmmc.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI1_Init) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI1_Init) refers to spi.o(.bss) for .bss
    spi.o(i.MX_SPI2_Init) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI2_Init) refers to spi.o(.bss) for .bss
    spi.o(i.MX_SPI3_Init) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI3_Init) refers to spi.o(.bss) for .bss
    spi.o(i.SPI2_WriteByte) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.SPI2_WriteByte) refers to spi.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM16_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM16_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM16_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM16_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM16_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM16_Init) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM16_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    stm32l4xx_it.o(i.ADC1_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32l4xx_it.o(i.ADC1_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32l4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32l4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32l4xx_it.o(i.DMA1_Channel4_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32l4xx_it.o(i.DMA1_Channel4_IRQHandler) refers to usart.o(.bss) for hdma_usart1_tx
    stm32l4xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32l4xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32l4xx_it.o(i.EXTI2_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it.o(i.EXTI3_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it.o(i.LPUART1_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32l4xx_it.o(i.LPUART1_IRQHandler) refers to usart.o(.bss) for hlpuart1
    stm32l4xx_it.o(i.SDMMC1_IRQHandler) refers to stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) for HAL_SD_IRQHandler
    stm32l4xx_it.o(i.SDMMC1_IRQHandler) refers to sdmmc.o(.bss) for hsd1
    stm32l4xx_it.o(i.SysTick_Handler) refers to stm32l4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32l4xx_it.o(i.TIM2_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32l4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32l4xx_it.o(i.USART3_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32l4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32l4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32l4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAError) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32l4xx_hal_adc.o(i.ADC_Disable) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_Enable) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_Enable) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_ConfigAnalogWDThresholds) for LL_ADC_ConfigAnalogWDThresholds
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32l4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32l4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal.o(i.HAL_DeInit) refers to stm32l4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTickFreq) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTickPrio) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_IncTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_InitTick) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_SetTickFreq) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_SetTickFreq) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to system_stm32l4xx.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) for RCC_SetFlashLatencyFromMSIRange
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange) for HAL_PWREx_GetVoltageRange
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_GetSAIxPeriphCLKFreq) for RCCEx_GetSAIxPeriphCLKFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to system_stm32l4xx.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) for RCCEx_PLLSAI1_Config
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention) for HAL_PWREx_SetSRAM2ContentRetention
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention) for HAL_PWREx_SetSRAM2ContentRetention
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32l4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32l4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32l4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32l4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32l4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32l4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32l4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32l4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32l4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32l4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32l4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32l4xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32l4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32l4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32l4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32l4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32l4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32l4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32l4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32l4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32l4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_SelfCalibrate) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback) for HAL_QSPI_AbortCpltCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMAAbortCplt) for QSPI_DMAAbortCplt
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling) refers to stm32l4xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT) refers to stm32l4xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command) refers to stm32l4xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command_IT) refers to stm32l4xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_DeInit) refers to quadspi.o(i.HAL_QSPI_MspDeInit) for HAL_QSPI_MspDeInit
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_FifoThresholdCallback) for HAL_QSPI_FifoThresholdCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_TxCpltCallback) for HAL_QSPI_TxCpltCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_RxCpltCallback) for HAL_QSPI_RxCpltCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_CmdCpltCallback) for HAL_QSPI_CmdCpltCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback) for HAL_QSPI_AbortCpltCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback) for HAL_QSPI_ErrorCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_StatusMatchCallback) for HAL_QSPI_StatusMatchCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_TimeOutCallback) for HAL_QSPI_TimeOutCallback
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMAAbortCplt) for QSPI_DMAAbortCplt
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Init) refers to quadspi.o(i.HAL_QSPI_MspInit) for HAL_QSPI_MspInit
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Init) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped) refers to stm32l4xx_hal_qspi.o(i.QSPI_Config) for QSPI_Config
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMARxCplt) for QSPI_DMARxCplt
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMARxHalfCplt) for QSPI_DMARxHalfCplt
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMAError) for QSPI_DMAError
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit) refers to stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) for QSPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMATxCplt) for QSPI_DMATxCplt
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMATxHalfCplt) for QSPI_DMATxHalfCplt
    stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA) refers to stm32l4xx_hal_qspi.o(i.QSPI_DMAError) for QSPI_DMAError
    stm32l4xx_hal_qspi.o(i.QSPI_DMAAbortCplt) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback) for HAL_QSPI_ErrorCallback
    stm32l4xx_hal_qspi.o(i.QSPI_DMAError) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort_IT) for HAL_QSPI_Abort_IT
    stm32l4xx_hal_qspi.o(i.QSPI_DMARxHalfCplt) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_RxHalfCpltCallback) for HAL_QSPI_RxHalfCpltCallback
    stm32l4xx_hal_qspi.o(i.QSPI_DMATxHalfCplt) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_TxHalfCpltCallback) for HAL_QSPI_TxHalfCpltCallback
    stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7) for SDMMC_GetCmdResp7
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) for SDMMC_GetCmdResp6
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSleepMmc) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSleepMmc) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand) for SDMMC_SendCommand
    stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_ll_sdmmc.o(i.SDMMC_PowerState_ON) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32l4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32l4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32l4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32l4xx_hal_sd.o(i.SD_FindSCR) for SD_FindSCR
    stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) for SDMMC_CmdBusWidth
    stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_Init) for SDMMC_Init
    stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32l4xx_hal_sd.o(i.HAL_SD_DeInit) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_PowerState_OFF) for SDMMC_PowerState_OFF
    stm32l4xx_hal_sd.o(i.HAL_SD_DeInit) refers to sdmmc.o(i.HAL_SD_MspDeInit) for HAL_SD_MspDeInit
    stm32l4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) for SDMMC_CmdSDEraseStartAdd
    stm32l4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) for SDMMC_CmdSDEraseEndAdd
    stm32l4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdErase) for SDMMC_CmdErase
    stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) for SDMMC_CmdSendStatus
    stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_hal_sd.o(i.HAL_SD_GetCardStatus) refers to stm32l4xx_hal_sd.o(i.SD_SendSDStatus) for SD_SendSDStatus
    stm32l4xx_hal_sd.o(i.HAL_SD_GetCardStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ReadFIFO) for SDMMC_ReadFIFO
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32l4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_TxCpltCallback) for HAL_SD_TxCpltCallback
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_WriteFIFO) for SDMMC_WriteFIFO
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32l4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32l4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32l4xx_hal_sd.o(i.HAL_SD_Init) refers to sdmmc.o(i.HAL_SD_MspInit) for HAL_SD_MspInit
    stm32l4xx_hal_sd.o(i.HAL_SD_Init) refers to stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) for HAL_SD_InitCard
    stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_Init) for SDMMC_Init
    stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_PowerState_ON) for SDMMC_PowerState_ON
    stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32l4xx_hal_sd.o(i.SD_PowerON) for SD_PowerON
    stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32l4xx_hal_sd.o(i.SD_InitCard) for SD_InitCard
    stm32l4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ReadFIFO) for SDMMC_ReadFIFO
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32l4xx_hal_sd.o(i.SD_DMAReceiveCplt) for SD_DMAReceiveCplt
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32l4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_WriteFIFO) for SDMMC_WriteFIFO
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32l4xx_hal_sd.o(i.SD_DMATransmitCplt) for SD_DMATransmitCplt
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32l4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.SD_DMAError) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32l4xx_hal_sd.o(i.SD_DMAError) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.SD_DMAError) refers to stm32l4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32l4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32l4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32l4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32l4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32l4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32l4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32l4xx_hal_sd.o(i.SD_DMARxAbort) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32l4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32l4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32l4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32l4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32l4xx_hal_sd.o(i.SD_DMATxAbort) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32l4xx_hal_sd.o(i.SD_FindSCR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_sd.o(i.SD_FindSCR) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32l4xx_hal_sd.o(i.SD_FindSCR) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32l4xx_hal_sd.o(i.SD_FindSCR) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.SD_FindSCR) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) for SDMMC_CmdSendSCR
    stm32l4xx_hal_sd.o(i.SD_FindSCR) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ReadFIFO) for SDMMC_ReadFIFO
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetPowerState) for SDMMC_GetPowerState
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) for SDMMC_CmdSendCID
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) for SDMMC_CmdSetRelAdd
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) for SDMMC_CmdSendCSD
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardCSD) for HAL_SD_GetCardCSD
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) for SDMMC_CmdSelDesel
    stm32l4xx_hal_sd.o(i.SD_InitCard) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_Init) for SDMMC_Init
    stm32l4xx_hal_sd.o(i.SD_PowerON) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) for SDMMC_CmdGoIdleState
    stm32l4xx_hal_sd.o(i.SD_PowerON) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) for SDMMC_CmdOperCond
    stm32l4xx_hal_sd.o(i.SD_PowerON) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32l4xx_hal_sd.o(i.SD_PowerON) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) for SDMMC_CmdAppOperCommand
    stm32l4xx_hal_sd.o(i.SD_PowerON) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse) for SDMMC_GetResponse
    stm32l4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32l4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32l4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData) for SDMMC_ConfigData
    stm32l4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) for SDMMC_CmdStatusRegister
    stm32l4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32l4xx_ll_sdmmc.o(i.SDMMC_ReadFIFO) for SDMMC_ReadFIFO
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAError) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    system_stm32l4xx.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx.o(.constdata) for .constdata
    system_stm32l4xx.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx.o(.data) for .data
    hzlib.o(i.GetGBKCode) refers to memcpya.o(.text) for __aeabi_memcpy
    hzlib.o(i.GetGBKCode) refers to hzlib.o(.constdata) for .constdata
    lcd.o(i.LCD_Address_Set) refers to lcd.o(i.LCD_Write_Cmd) for LCD_Write_Cmd
    lcd.o(i.LCD_Address_Set) refers to lcd.o(i.LCD_Write_Data) for LCD_Write_Data
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_Clear) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_SPI_Send) for LCD_SPI_Send
    lcd.o(i.LCD_Clear) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_DisplayOff) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_DisplayOn) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_DrawLine) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_SPI_Send) for LCD_SPI_Send
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_Draw_Point) for LCD_Draw_Point
    lcd.o(i.LCD_DrawLine) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_DrawLine) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_DrawRectangle) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Draw_Circle) refers to lcd.o(i.LCD_Draw_Point) for LCD_Draw_Point
    lcd.o(i.LCD_Draw_ColorPoint) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_Draw_ColorPoint) refers to lcd.o(i.LCD_Write_HalfWord) for LCD_Write_HalfWord
    lcd.o(i.LCD_Draw_Point) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_Draw_Point) refers to lcd.o(i.LCD_Write_HalfWord) for LCD_Write_HalfWord
    lcd.o(i.LCD_Draw_Point) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_Fill) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_SPI_Send) for LCD_SPI_Send
    lcd.o(i.LCD_Fill) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Write_Cmd) for LCD_Write_Cmd
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Write_Data) for LCD_Write_Data
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    lcd.o(i.LCD_SPI_Send) refers to spi.o(i.SPI2_WriteByte) for SPI2_WriteByte
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_Write_HalfWord) for LCD_Write_HalfWord
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowString) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_Show_Image) refers to lcd.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_Show_Image) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Show_Image) refers to lcd.o(i.LCD_SPI_Send) for LCD_SPI_Send
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_Write_Cmd) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Write_Cmd) refers to lcd.o(i.LCD_SPI_Send) for LCD_SPI_Send
    lcd.o(i.LCD_Write_Data) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Write_Data) refers to lcd.o(i.LCD_SPI_Send) for LCD_SPI_Send
    lcd.o(i.LCD_Write_HalfWord) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.LCD_Write_HalfWord) refers to lcd.o(i.LCD_SPI_Send) for LCD_SPI_Send
    lcd.o(i.PutChinese) refers to hzlib.o(i.GetGBKCode) for GetGBKCode
    lcd.o(i.PutChinese) refers to lcd.o(i.LCD_Draw_Point) for LCD_Draw_Point
    lcd.o(i.PutChinese) refers to lcd.o(i.LCD_Draw_ColorPoint) for LCD_Draw_ColorPoint
    lcd.o(i.PutChinese) refers to lcd.o(.data) for .data
    lcd.o(i.PutChinese_strings) refers to lcd.o(i.PutChinese) for PutChinese
    flash.o(i.Flash_ErasePages) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    flash.o(i.Flash_ErasePages) refers to flash.o(i.Flash_GetBank) for Flash_GetBank
    flash.o(i.Flash_ErasePages) refers to flash.o(i.Flash_GetPage) for Flash_GetPage
    flash.o(i.Flash_ErasePages) refers to stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    flash.o(i.Flash_ErasePages) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    flash.o(i.Flash_Write32) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    flash.o(i.Flash_Write32) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    flash.o(i.Flash_Write32) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    flash.o(i.Flash_Write64) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    flash.o(i.Flash_Write64) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    flash.o(i.Flash_Write64) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    flash.o(i.Flash_WriteDoubleWord) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    flash.o(i.Flash_WriteDoubleWord) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    flash.o(i.Flash_WriteDoubleWord) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    flash.o(i.Flash_WriteOneWord) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    flash.o(i.Flash_WriteOneWord) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    flash.o(i.Flash_WriteOneWord) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    flash.o(i.Flash_WriteReadTest) refers to printfa.o(i.__0printf) for __2printf
    flash.o(i.Flash_WriteReadTest) refers to flash.o(i.Flash_ErasePages) for Flash_ErasePages
    flash.o(i.Flash_WriteReadTest) refers to flash.o(i.Flash_Write32) for Flash_Write32
    flash.o(i.Flash_WriteReadTest) refers to flash.o(i.Flash_ReadBytes) for Flash_ReadBytes
    hal_qspi_flash.o(i.QSPI_Receive) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive) for HAL_QSPI_Receive
    hal_qspi_flash.o(i.QSPI_Receive) refers to quadspi.o(.bss) for hqspi
    hal_qspi_flash.o(i.QSPI_Send_CMD) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command) for HAL_QSPI_Command
    hal_qspi_flash.o(i.QSPI_Send_CMD) refers to quadspi.o(.bss) for hqspi
    hal_qspi_flash.o(i.QSPI_Transmit) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit) for HAL_QSPI_Transmit
    hal_qspi_flash.o(i.QSPI_Transmit) refers to quadspi.o(.bss) for hqspi
    hal_qspi_flash.o(i.hal_spi_flash_erase) refers to hal_qspi_flash.o(i.prv_spi_flash_erase_sector) for prv_spi_flash_erase_sector
    hal_qspi_flash.o(i.hal_spi_flash_erase_write) refers to hal_qspi_flash.o(i.hal_spi_flash_erase) for hal_spi_flash_erase
    hal_qspi_flash.o(i.hal_spi_flash_erase_write) refers to hal_qspi_flash.o(i.hal_spi_flash_write) for hal_spi_flash_write
    hal_qspi_flash.o(i.hal_spi_flash_get_id) refers to stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command) for HAL_QSPI_Command
    hal_qspi_flash.o(i.hal_spi_flash_get_id) refers to hal_qspi_flash.o(i.QSPI_Receive) for QSPI_Receive
    hal_qspi_flash.o(i.hal_spi_flash_get_id) refers to quadspi.o(.bss) for hqspi
    hal_qspi_flash.o(i.hal_spi_flash_read) refers to hal_qspi_flash.o(i.QSPI_Send_CMD) for QSPI_Send_CMD
    hal_qspi_flash.o(i.hal_spi_flash_read) refers to hal_qspi_flash.o(i.QSPI_Receive) for QSPI_Receive
    hal_qspi_flash.o(i.hal_spi_flash_write) refers to hal_qspi_flash.o(i.prv_spi_flash_write_page) for prv_spi_flash_write_page
    hal_qspi_flash.o(i.prv_spi_flash_erase_sector) refers to hal_qspi_flash.o(i.prv_spi_flash_write_enable) for prv_spi_flash_write_enable
    hal_qspi_flash.o(i.prv_spi_flash_erase_sector) refers to hal_qspi_flash.o(i.prv_spi_flash_wait_write_end) for prv_spi_flash_wait_write_end
    hal_qspi_flash.o(i.prv_spi_flash_erase_sector) refers to hal_qspi_flash.o(i.QSPI_Send_CMD) for QSPI_Send_CMD
    hal_qspi_flash.o(i.prv_spi_flash_wait_write_end) refers to hal_qspi_flash.o(i.QSPI_Send_CMD) for QSPI_Send_CMD
    hal_qspi_flash.o(i.prv_spi_flash_wait_write_end) refers to hal_qspi_flash.o(i.QSPI_Receive) for QSPI_Receive
    hal_qspi_flash.o(i.prv_spi_flash_write_enable) refers to hal_qspi_flash.o(i.QSPI_Send_CMD) for QSPI_Send_CMD
    hal_qspi_flash.o(i.prv_spi_flash_write_page) refers to hal_qspi_flash.o(i.prv_spi_flash_write_enable) for prv_spi_flash_write_enable
    hal_qspi_flash.o(i.prv_spi_flash_write_page) refers to hal_qspi_flash.o(i.QSPI_Send_CMD) for QSPI_Send_CMD
    hal_qspi_flash.o(i.prv_spi_flash_write_page) refers to hal_qspi_flash.o(i.QSPI_Transmit) for QSPI_Transmit
    hal_qspi_flash.o(i.prv_spi_flash_write_page) refers to hal_qspi_flash.o(i.prv_spi_flash_wait_write_end) for prv_spi_flash_wait_write_end
    e53_ia1.o(i.Convert_BH1750) refers to e53_ia1.o(i.Start_BH1750) for Start_BH1750
    e53_ia1.o(i.Convert_BH1750) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    e53_ia1.o(i.Convert_BH1750) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    e53_ia1.o(i.Convert_BH1750) refers to dflti.o(.text) for __aeabi_i2d
    e53_ia1.o(i.Convert_BH1750) refers to ddiv.o(.text) for __aeabi_ddiv
    e53_ia1.o(i.Convert_BH1750) refers to d2f.o(.text) for __aeabi_d2f
    e53_ia1.o(i.Convert_BH1750) refers to i2c.o(.bss) for hi2c1
    e53_ia1.o(i.E53_IA1_Init) refers to e53_ia1.o(i.Init_BH1750) for Init_BH1750
    e53_ia1.o(i.E53_IA1_Init) refers to e53_ia1.o(i.Init_SHT30) for Init_SHT30
    e53_ia1.o(i.E53_IA1_Light_Set) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    e53_ia1.o(i.E53_IA1_Motor_Set) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    e53_ia1.o(i.E53_IA1_Motor_Set) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    e53_ia1.o(i.E53_IA1_Motor_Set) refers to tim.o(.bss) for htim16
    e53_ia1.o(i.E53_IA1_Read_Data) refers to e53_ia1.o(i.Convert_BH1750) for Convert_BH1750
    e53_ia1.o(i.E53_IA1_Read_Data) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    e53_ia1.o(i.E53_IA1_Read_Data) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    e53_ia1.o(i.E53_IA1_Read_Data) refers to e53_ia1.o(i.SHT3x_CheckCrc) for SHT3x_CheckCrc
    e53_ia1.o(i.E53_IA1_Read_Data) refers to e53_ia1.o(i.SHT3x_CalcTemperatureC) for SHT3x_CalcTemperatureC
    e53_ia1.o(i.E53_IA1_Read_Data) refers to e53_ia1.o(i.SHT3x_CalcRH) for SHT3x_CalcRH
    e53_ia1.o(i.E53_IA1_Read_Data) refers to i2c.o(.bss) for hi2c1
    e53_ia1.o(i.Init_BH1750) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    e53_ia1.o(i.Init_BH1750) refers to i2c.o(.bss) for hi2c1
    e53_ia1.o(i.Init_SHT30) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    e53_ia1.o(i.Init_SHT30) refers to i2c.o(.bss) for hi2c1
    e53_ia1.o(i.SHT30_reset) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    e53_ia1.o(i.SHT30_reset) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    e53_ia1.o(i.SHT30_reset) refers to i2c.o(.bss) for hi2c1
    e53_ia1.o(i.Start_BH1750) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    e53_ia1.o(i.Start_BH1750) refers to i2c.o(.bss) for hi2c1
    fatfs.o(i.MX_FATFS_Init) refers to ff_gen_drv.o(i.FATFS_LinkDriver) for FATFS_LinkDriver
    fatfs.o(i.MX_FATFS_Init) refers to fatfs.o(.data) for .data
    fatfs.o(i.MX_FATFS_Init) refers to sd_diskio.o(.constdata) for SD_Driver
    sd_diskio.o(i.SD_CheckStatus) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(i.SD_CheckStatus) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_initialize) refers to bsp_driver_sd.o(i.BSP_SD_Init) for BSP_SD_Init
    sd_diskio.o(i.SD_initialize) refers to sd_diskio.o(i.SD_CheckStatus) for SD_CheckStatus
    sd_diskio.o(i.SD_initialize) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_ioctl) refers to bsp_driver_sd.o(i.BSP_SD_GetCardInfo) for BSP_SD_GetCardInfo
    sd_diskio.o(i.SD_ioctl) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_read) refers to bsp_driver_sd.o(i.BSP_SD_ReadBlocks) for BSP_SD_ReadBlocks
    sd_diskio.o(i.SD_read) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(i.SD_status) refers to sd_diskio.o(i.SD_CheckStatus) for SD_CheckStatus
    sd_diskio.o(i.SD_write) refers to bsp_driver_sd.o(i.BSP_SD_WriteBlocks) for BSP_SD_WriteBlocks
    sd_diskio.o(i.SD_write) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_initialize) for SD_initialize
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_status) for SD_status
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_read) for SD_read
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_write) for SD_write
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_ioctl) for SD_ioctl
    bsp_driver_sd.o(i.BSP_SD_Erase) refers to stm32l4xx_hal_sd.o(i.HAL_SD_Erase) for HAL_SD_Erase
    bsp_driver_sd.o(i.BSP_SD_Erase) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.BSP_SD_GetCardInfo) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardInfo) for HAL_SD_GetCardInfo
    bsp_driver_sd.o(i.BSP_SD_GetCardInfo) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.BSP_SD_GetCardState) refers to stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    bsp_driver_sd.o(i.BSP_SD_GetCardState) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.BSP_SD_Init) refers to bsp_driver_sd.o(i.BSP_SD_IsDetected) for BSP_SD_IsDetected
    bsp_driver_sd.o(i.BSP_SD_Init) refers to stm32l4xx_hal_sd.o(i.HAL_SD_Init) for HAL_SD_Init
    bsp_driver_sd.o(i.BSP_SD_Init) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks) refers to stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks) for HAL_SD_ReadBlocks
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA) refers to stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) for HAL_SD_ReadBlocks_DMA
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks) refers to stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks) for HAL_SD_WriteBlocks
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA) refers to stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) for HAL_SD_WriteBlocks_DMA
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA) refers to sdmmc.o(.bss) for hsd1
    bsp_driver_sd.o(i.HAL_SD_AbortCallback) refers to bsp_driver_sd.o(i.BSP_SD_AbortCallback) for BSP_SD_AbortCallback
    bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) refers to bsp_driver_sd.o(i.BSP_SD_ReadCpltCallback) for BSP_SD_ReadCpltCallback
    bsp_driver_sd.o(i.HAL_SD_TxCpltCallback) refers to bsp_driver_sd.o(i.BSP_SD_WriteCpltCallback) for BSP_SD_WriteCpltCallback
    diskio.o(i.disk_initialize) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_ioctl) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_read) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_status) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_write) refers to ff_gen_drv.o(.bss) for disk
    ff.o(i.check_fs) refers to ff.o(i.move_window) for move_window
    ff.o(i.check_fs) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.check_fs) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.chk_lock) refers to ff.o(.bss) for .bss
    ff.o(i.clear_lock) refers to ff.o(.bss) for .bss
    ff.o(i.cmp_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.cmp_lfn) refers to cc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to cc936.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to cc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dec_lock) refers to ff.o(.bss) for .bss
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.st_word) for st_word
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_register) refers to ff.o(.constdata) for .constdata
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_close) refers to ff.o(i.validate) for validate
    ff.o(i.f_close) refers to ff.o(i.dec_lock) for dec_lock
    ff.o(i.f_closedir) refers to ff.o(i.validate) for validate
    ff.o(i.f_closedir) refers to ff.o(i.dec_lock) for dec_lock
    ff.o(i.f_getfree) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_getfree) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_getfree) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.f_mkdir) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkdir) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_mkfs) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.f_mkfs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkfs) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkfs) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_mkfs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkfs) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_mkfs) refers to ff.o(.data) for .data
    ff.o(i.f_mkfs) refers to ff.o(.constdata) for .constdata
    ff.o(i.f_mount) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mount) refers to ff.o(i.clear_lock) for clear_lock
    ff.o(i.f_mount) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mount) refers to ff.o(.data) for .data
    ff.o(i.f_open) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_open) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_open) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to ff.o(i.inc_lock) for inc_lock
    ff.o(i.f_open) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_open) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_open) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_open) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_open) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_open) refers to ff.o(.bss) for .bss
    ff.o(i.f_opendir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_opendir) refers to ff.o(i.inc_lock) for inc_lock
    ff.o(i.f_printf) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_printf) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_putc) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_putc) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_puts) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_puts) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_rename) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_rename) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_stat) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_sync) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_sync) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_sync) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_unlink) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_unlink) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.find_volume) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.find_volume) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.find_volume) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.find_volume) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.find_volume) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.find_volume) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.find_volume) refers to ff.o(i.move_window) for move_window
    ff.o(i.find_volume) refers to ff.o(i.clear_lock) for clear_lock
    ff.o(i.find_volume) refers to ff.o(.data) for .data
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.follow_path) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fat) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.get_fileinfo) refers to cc936.o(i.ff_convert) for ff_convert
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.inc_lock) refers to ff.o(.bss) for .bss
    ff.o(i.ld_clust) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.move_window) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.put_fat) refers to ff.o(i.st_word) for st_word
    ff.o(i.put_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.put_fat) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.putc_bfd) refers to ff.o(i.f_write) for f_write
    ff.o(i.putc_flush) refers to ff.o(i.f_write) for f_write
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.st_clust) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.sync_fs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync_fs) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.sync_fs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync_fs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.sync_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    ff_gen_drv.o(i.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.bss) for .bss
    ff_gen_drv.o(i.FATFS_LinkDriver) refers to ff_gen_drv.o(i.FATFS_LinkDriverEx) for FATFS_LinkDriverEx
    ff_gen_drv.o(i.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.bss) for .bss
    ff_gen_drv.o(i.FATFS_UnLinkDriver) refers to ff_gen_drv.o(i.FATFS_UnLinkDriverEx) for FATFS_UnLinkDriverEx
    ff_gen_drv.o(i.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.bss) for .bss
    cc936.o(i.ff_convert) refers to cc936.o(.constdata) for .constdata
    cc936.o(i.ff_wtoupper) refers to cc936.o(.constdata) for .constdata
    esp8266.o(i.esp8266_init) refers to usart.o(i.uart_print) for uart_print
    esp8266.o(i.esp8266_init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_init) refers to usart.o(.bss) for hlpuart1
    esp8266.o(i.esp8266_publish) refers to f2d.o(.text) for __aeabi_f2d
    esp8266.o(i.esp8266_publish) refers to printfa.o(i.__0sprintf) for __2sprintf
    esp8266.o(i.esp8266_publish) refers to usart.o(i.uart_print) for uart_print
    esp8266.o(i.esp8266_publish) refers to usart.o(.bss) for hlpuart1
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32l431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32l431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32l431xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.esp8266_publish_ack), (176 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (60 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.HAL_DAC_MspDeInit), (40 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (164 bytes).
    Removing quadspi.o(.rev16_text), (4 bytes).
    Removing quadspi.o(.revsh_text), (4 bytes).
    Removing quadspi.o(.rrx_text), (6 bytes).
    Removing quadspi.o(i.HAL_QSPI_MspDeInit), (44 bytes).
    Removing sdmmc.o(.rev16_text), (4 bytes).
    Removing sdmmc.o(.revsh_text), (4 bytes).
    Removing sdmmc.o(.rrx_text), (6 bytes).
    Removing sdmmc.o(i.HAL_SD_MspDeInit), (68 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (132 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (60 bytes).
    Removing tim.o(i.MX_TIM2_Init), (92 bytes).
    Removing stm32l4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_ConversionStop), (228 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt), (104 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_Disable), (112 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_Enable), (172 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (586 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit), (364 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (174 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (200 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start), (136 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (180 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT), (224 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (122 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32l4xx_hal_adc.o(i.LL_ADC_ConfigAnalogWDThresholds), (26 bytes).
    Removing stm32l4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (44 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (22 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (122 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (144 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (48 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (32 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (48 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (36 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1198 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (46 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (186 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (152 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (212 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (84 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (92 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (138 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (102 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (12 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (8 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing), (8 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime), (30 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState), (16 bytes).
    Removing stm32l4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DeInit), (48 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_SRAM2Erase), (28 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32l4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (56 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (298 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (124 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (304 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (342 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (342 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (160 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (304 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (362 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (268 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (152 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (350 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (268 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (152 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (320 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (224 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (364 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (200 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (364 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (200 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (324 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (212 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAAbort), (20 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAError), (18 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (82 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (36 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ), (94 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ), (112 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt), (140 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITError), (244 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt), (240 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (76 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt), (304 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (114 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (272 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT), (296 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (296 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (308 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (100 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (100 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (266 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (288 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (78 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (78 bytes).
    Removing stm32l4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit), (224 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (228 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetResetSource), (24 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (36 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (132 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (108 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (88 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1), (80 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (140 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (56 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1), (156 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (204 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_StandbyMSIRangeConfig), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32l4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (28 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_Fast), (44 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (100 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (232 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program), (170 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (120 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32l4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase), (28 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (104 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (248 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase), (40 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (222 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (140 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (148 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (278 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (322 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32l4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.DMA_SetConfig), (46 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_DeInit), (156 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (218 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (72 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (212 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (92 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (88 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (68 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (132 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (132 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (8 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (52 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP2Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (60 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention), (40 bytes).
    Removing stm32l4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Disable), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Enable), (24 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32l4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (128 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (164 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (176 bytes).
    Removing stm32l4xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32l4xx_hal_dac.o(i.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32l4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (10 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_DeInit), (30 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_GetValue), (12 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_IRQHandler), (104 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_SetValue), (26 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_Start), (88 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_Start_DMA), (272 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_Stop), (26 bytes).
    Removing stm32l4xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (72 bytes).
    Removing stm32l4xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (10 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (24 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_GetTrimOffset), (18 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (56 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_SelfCalibrate), (274 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_SetUserTrimming), (64 bytes).
    Removing stm32l4xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (56 bytes).
    Removing stm32l4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (72 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (72 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (112 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_Init), (136 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (48 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (48 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (134 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort), (226 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive), (162 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (180 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT), (260 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAPause), (138 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAResume), (130 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop), (140 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (64 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (64 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive), (254 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (84 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (112 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAError), (76 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt), (128 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback), (62 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt), (28 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (38 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback), (52 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_EndTxTransfer), (24 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA), (164 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT), (80 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT), (76 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (136 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableClockStopMode), (48 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableClockStopMode), (48 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (310 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (80 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (134 bytes).
    Removing stm32l4xx_hal_qspi.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_qspi.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_qspi.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort), (144 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Abort_IT), (128 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling), (178 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_AutoPolling_IT), (160 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_CmdCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command), (140 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Command_IT), (128 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_DeInit), (40 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_FifoThresholdCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_GetError), (4 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_GetFifoThreshold), (12 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_GetState), (6 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_IRQHandler), (484 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_MemoryMapped), (140 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_MspInit), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive), (196 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive_DMA), (276 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Receive_IT), (120 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_SetFifoThreshold), (66 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_SetFlashID), (58 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_SetTimeout), (4 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_StatusMatchCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_TimeOutCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit), (188 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit_DMA), (256 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_Transmit_IT), (110 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.HAL_QSPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_qspi.o(i.QSPI_Config), (350 bytes).
    Removing stm32l4xx_hal_qspi.o(i.QSPI_DMAAbortCplt), (58 bytes).
    Removing stm32l4xx_hal_qspi.o(i.QSPI_DMAError), (30 bytes).
    Removing stm32l4xx_hal_qspi.o(i.QSPI_DMARxCplt), (18 bytes).
    Removing stm32l4xx_hal_qspi.o(i.QSPI_DMARxHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_qspi.o(i.QSPI_DMATxCplt), (18 bytes).
    Removing stm32l4xx_hal_qspi.o(i.QSPI_DMATxHalfCplt), (10 bytes).
    Removing stm32l4xx_ll_sdmmc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_ll_sdmmc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_ll_sdmmc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdErase), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition), (44 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR), (48 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc), (52 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSleepMmc), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister), (48 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock), (50 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_ConfigData), (36 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCommandResponse), (6 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_GetDataCounter), (4 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_GetFIFOCount), (6 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_PowerState_OFF), (8 bytes).
    Removing stm32l4xx_ll_sdmmc.o(i.SDMMC_SetSDMMCReadWaitMode), (14 bytes).
    Removing stm32l4xx_hal_sd.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_sd.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_sd.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_Abort), (120 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_AbortCallback), (2 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_Abort_IT), (156 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation), (338 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_DeInit), (38 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_Erase), (196 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_GetCardCID), (80 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_GetCardInfo), (36 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_GetCardStatus), (168 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_GetError), (4 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_GetState), (6 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_MspInit), (2 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks), (478 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA), (268 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT), (192 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks), (434 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA), (252 bytes).
    Removing stm32l4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT), (180 bytes).
    Removing stm32l4xx_hal_sd.o(i.SD_DMAError), (92 bytes).
    Removing stm32l4xx_hal_sd.o(i.SD_DMAReceiveCplt), (66 bytes).
    Removing stm32l4xx_hal_sd.o(i.SD_DMATransmitCplt), (14 bytes).
    Removing stm32l4xx_hal_sd.o(i.SD_FindSCR), (218 bytes).
    Removing stm32l4xx_hal_sd.o(i.SD_SendSDStatus), (232 bytes).
    Removing stm32l4xx_hal_sd_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_sd_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_sd_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort), (368 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (292 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop), (66 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (252 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive), (398 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (352 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (200 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (616 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (444 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (196 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (284 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (156 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (54 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (102 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (50 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (72 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR), (132 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR), (216 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (82 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR), (64 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR), (76 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError), (18 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (108 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (128 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (92 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (124 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction), (116 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi_ex.o(i.HAL_SPIEx_FlushRxFifo), (36 bytes).
    Removing stm32l4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (100 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (152 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (88 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (224 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (300 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (168 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (434 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (54 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (286 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (100 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init), (98 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (108 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (184 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (172 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (110 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (100 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init), (98 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start), (208 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (464 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (252 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (152 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (236 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (216 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (240 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (124 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (144 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (120 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (140 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (100 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (208 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (464 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (252 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (152 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (236 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (216 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (144 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig), (80 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (170 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (56 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (212 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (128 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (184 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (140 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (108 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (152 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (352 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (192 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (152 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (352 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (192 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (72 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing system_stm32l4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32l4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32l4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32l4xx.o(i.SystemCoreClockUpdate), (168 bytes).
    Removing hzlib.o(i.GetGBKCode), (40 bytes).
    Removing hzlib.o(.constdata), (216576 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (16 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (16 bytes).
    Removing lcd.o(i.LCD_DrawLine), (252 bytes).
    Removing lcd.o(i.LCD_DrawRectangle), (58 bytes).
    Removing lcd.o(i.LCD_Draw_Circle), (212 bytes).
    Removing lcd.o(i.LCD_Draw_ColorPoint), (22 bytes).
    Removing lcd.o(i.LCD_Draw_Point), (28 bytes).
    Removing lcd.o(i.LCD_Fill), (128 bytes).
    Removing lcd.o(i.LCD_Pow), (16 bytes).
    Removing lcd.o(i.LCD_ShowNum), (112 bytes).
    Removing lcd.o(i.LCD_Show_Image), (104 bytes).
    Removing lcd.o(i.LCD_ShowxNum), (120 bytes).
    Removing lcd.o(i.PutChinese), (134 bytes).
    Removing lcd.o(i.PutChinese_strings), (42 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(.rrx_text), (6 bytes).
    Removing flash.o(i.Flash_ErasePages), (104 bytes).
    Removing flash.o(i.Flash_GetBank), (28 bytes).
    Removing flash.o(i.Flash_GetPage), (64 bytes).
    Removing flash.o(i.Flash_Read32), (32 bytes).
    Removing flash.o(i.Flash_ReadBytes), (32 bytes).
    Removing flash.o(i.Flash_ReadOneWord), (4 bytes).
    Removing flash.o(i.Flash_Write32), (80 bytes).
    Removing flash.o(i.Flash_Write64), (60 bytes).
    Removing flash.o(i.Flash_WriteDoubleWord), (56 bytes).
    Removing flash.o(i.Flash_WriteOneWord), (56 bytes).
    Removing flash.o(i.Flash_WriteReadTest), (160 bytes).
    Removing hal_qspi_flash.o(.rev16_text), (4 bytes).
    Removing hal_qspi_flash.o(.revsh_text), (4 bytes).
    Removing hal_qspi_flash.o(.rrx_text), (6 bytes).
    Removing hal_qspi_flash.o(i.QSPI_Receive), (36 bytes).
    Removing hal_qspi_flash.o(i.QSPI_Send_CMD), (56 bytes).
    Removing hal_qspi_flash.o(i.QSPI_Transmit), (36 bytes).
    Removing hal_qspi_flash.o(i.hal_spi_flash_erase), (62 bytes).
    Removing hal_qspi_flash.o(i.hal_spi_flash_erase_write), (28 bytes).
    Removing hal_qspi_flash.o(i.hal_spi_flash_get_id), (92 bytes).
    Removing hal_qspi_flash.o(i.hal_spi_flash_read), (74 bytes).
    Removing hal_qspi_flash.o(i.hal_spi_flash_write), (288 bytes).
    Removing hal_qspi_flash.o(i.prv_spi_flash_erase_sector), (44 bytes).
    Removing hal_qspi_flash.o(i.prv_spi_flash_wait_write_end), (50 bytes).
    Removing hal_qspi_flash.o(i.prv_spi_flash_write_enable), (26 bytes).
    Removing hal_qspi_flash.o(i.prv_spi_flash_write_page), (62 bytes).
    Removing e53_ia1.o(.rev16_text), (4 bytes).
    Removing e53_ia1.o(.revsh_text), (4 bytes).
    Removing e53_ia1.o(.rrx_text), (6 bytes).
    Removing e53_ia1.o(i.E53_IA1_Light_Set), (28 bytes).
    Removing e53_ia1.o(i.E53_IA1_Motor_Set), (32 bytes).
    Removing e53_ia1.o(i.SHT30_reset), (40 bytes).
    Removing e53_ia1.o(.constdata), (2 bytes).
    Removing fatfs.o(.rev16_text), (4 bytes).
    Removing fatfs.o(.revsh_text), (4 bytes).
    Removing fatfs.o(.rrx_text), (6 bytes).
    Removing fatfs.o(i.MX_FATFS_Init), (28 bytes).
    Removing fatfs.o(i.get_fattime), (4 bytes).
    Removing fatfs.o(.bss), (564 bytes).
    Removing fatfs.o(.bss), (560 bytes).
    Removing fatfs.o(.data), (5 bytes).
    Removing sd_diskio.o(.rev16_text), (4 bytes).
    Removing sd_diskio.o(.revsh_text), (4 bytes).
    Removing sd_diskio.o(.rrx_text), (6 bytes).
    Removing sd_diskio.o(i.SD_CheckStatus), (32 bytes).
    Removing sd_diskio.o(i.SD_initialize), (32 bytes).
    Removing sd_diskio.o(i.SD_ioctl), (80 bytes).
    Removing sd_diskio.o(i.SD_read), (38 bytes).
    Removing sd_diskio.o(i.SD_status), (4 bytes).
    Removing sd_diskio.o(i.SD_write), (38 bytes).
    Removing sd_diskio.o(.constdata), (20 bytes).
    Removing sd_diskio.o(.data), (1 bytes).
    Removing bsp_driver_sd.o(.rev16_text), (4 bytes).
    Removing bsp_driver_sd.o(.revsh_text), (4 bytes).
    Removing bsp_driver_sd.o(.rrx_text), (6 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_Erase), (28 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_GetCardInfo), (12 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_GetCardState), (24 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_ITConfig), (4 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_Init), (28 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_IsDetected), (14 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_ReadBlocks), (32 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA), (28 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_WriteBlocks), (32 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA), (28 bytes).
    Removing syscall.o(.rev16_text), (4 bytes).
    Removing syscall.o(.revsh_text), (4 bytes).
    Removing syscall.o(.rrx_text), (6 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(.rrx_text), (6 bytes).
    Removing diskio.o(i.disk_initialize), (40 bytes).
    Removing diskio.o(i.disk_ioctl), (28 bytes).
    Removing diskio.o(i.disk_read), (28 bytes).
    Removing diskio.o(i.disk_status), (24 bytes).
    Removing diskio.o(i.disk_write), (28 bytes).
    Removing diskio.o(i.get_fattime), (4 bytes).
    Removing ff.o(.rev16_text), (4 bytes).
    Removing ff.o(.revsh_text), (4 bytes).
    Removing ff.o(.rrx_text), (6 bytes).
    Removing ff.o(i.check_fs), (112 bytes).
    Removing ff.o(i.chk_chr), (16 bytes).
    Removing ff.o(i.chk_lock), (92 bytes).
    Removing ff.o(i.clear_lock), (32 bytes).
    Removing ff.o(i.clmt_clust), (36 bytes).
    Removing ff.o(i.clust2sect), (24 bytes).
    Removing ff.o(i.cmp_lfn), (134 bytes).
    Removing ff.o(i.create_chain), (164 bytes).
    Removing ff.o(i.create_name), (586 bytes).
    Removing ff.o(i.dec_lock), (52 bytes).
    Removing ff.o(i.dir_find), (208 bytes).
    Removing ff.o(i.dir_next), (244 bytes).
    Removing ff.o(i.dir_read), (182 bytes).
    Removing ff.o(i.dir_register), (490 bytes).
    Removing ff.o(i.dir_remove), (78 bytes).
    Removing ff.o(i.dir_sdi), (130 bytes).
    Removing ff.o(i.f_close), (40 bytes).
    Removing ff.o(i.f_closedir), (34 bytes).
    Removing ff.o(i.f_getfree), (212 bytes).
    Removing ff.o(i.f_gets), (72 bytes).
    Removing ff.o(i.f_lseek), (536 bytes).
    Removing ff.o(i.f_mkdir), (348 bytes).
    Removing ff.o(i.f_mkfs), (1362 bytes).
    Removing ff.o(i.f_mount), (88 bytes).
    Removing ff.o(i.f_open), (532 bytes).
    Removing ff.o(i.f_opendir), (140 bytes).
    Removing ff.o(i.f_printf), (460 bytes).
    Removing ff.o(i.f_putc), (30 bytes).
    Removing ff.o(i.f_puts), (38 bytes).
    Removing ff.o(i.f_read), (344 bytes).
    Removing ff.o(i.f_readdir), (82 bytes).
    Removing ff.o(i.f_rename), (266 bytes).
    Removing ff.o(i.f_stat), (70 bytes).
    Removing ff.o(i.f_sync), (142 bytes).
    Removing ff.o(i.f_truncate), (162 bytes).
    Removing ff.o(i.f_unlink), (166 bytes).
    Removing ff.o(i.f_write), (404 bytes).
    Removing ff.o(i.find_volume), (596 bytes).
    Removing ff.o(i.follow_path), (116 bytes).
    Removing ff.o(i.gen_numname), (140 bytes).
    Removing ff.o(i.get_fat), (190 bytes).
    Removing ff.o(i.get_fileinfo), (244 bytes).
    Removing ff.o(i.get_ldnumber), (60 bytes).
    Removing ff.o(i.inc_lock), (128 bytes).
    Removing ff.o(i.ld_clust), (38 bytes).
    Removing ff.o(i.ld_dword), (22 bytes).
    Removing ff.o(i.ld_word), (10 bytes).
    Removing ff.o(i.mem_cpy), (18 bytes).
    Removing ff.o(i.mem_set), (14 bytes).
    Removing ff.o(i.move_window), (50 bytes).
    Removing ff.o(i.pick_lfn), (116 bytes).
    Removing ff.o(i.put_fat), (234 bytes).
    Removing ff.o(i.putc_bfd), (70 bytes).
    Removing ff.o(i.putc_flush), (42 bytes).
    Removing ff.o(i.remove_chain), (116 bytes).
    Removing ff.o(i.st_clust), (40 bytes).
    Removing ff.o(i.st_dword), (16 bytes).
    Removing ff.o(i.st_word), (8 bytes).
    Removing ff.o(i.sum_sfn), (26 bytes).
    Removing ff.o(i.sync_fs), (136 bytes).
    Removing ff.o(i.sync_window), (82 bytes).
    Removing ff.o(i.validate), (52 bytes).
    Removing ff.o(.bss), (32 bytes).
    Removing ff.o(.constdata), (42 bytes).
    Removing ff.o(.data), (8 bytes).
    Removing ff_gen_drv.o(.rev16_text), (4 bytes).
    Removing ff_gen_drv.o(.revsh_text), (4 bytes).
    Removing ff_gen_drv.o(.rrx_text), (6 bytes).
    Removing ff_gen_drv.o(i.FATFS_GetAttachedDriversNbr), (12 bytes).
    Removing ff_gen_drv.o(i.FATFS_LinkDriver), (6 bytes).
    Removing ff_gen_drv.o(i.FATFS_LinkDriverEx), (64 bytes).
    Removing ff_gen_drv.o(i.FATFS_UnLinkDriver), (6 bytes).
    Removing ff_gen_drv.o(i.FATFS_UnLinkDriverEx), (52 bytes).
    Removing ff_gen_drv.o(.bss), (12 bytes).
    Removing cc936.o(.rev16_text), (4 bytes).
    Removing cc936.o(.revsh_text), (4 bytes).
    Removing cc936.o(.rrx_text), (6 bytes).
    Removing cc936.o(i.ff_convert), (76 bytes).
    Removing cc936.o(i.ff_wtoupper), (128 bytes).
    Removing cc936.o(.constdata), (175030 bytes).
    Removing esp8266.o(.rev16_text), (4 bytes).
    Removing esp8266.o(.revsh_text), (4 bytes).
    Removing esp8266.o(.rrx_text), (6 bytes).

988 unused section(s) (total 469694 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../../../Core/Src/adc.c                  0x00000000   Number         0  adc.o ABSOLUTE
    ../../../Core/Src/dac.c                  0x00000000   Number         0  dac.o ABSOLUTE
    ../../../Core/Src/dma.c                  0x00000000   Number         0  dma.o ABSOLUTE
    ../../../Core/Src/gpio.c                 0x00000000   Number         0  gpio.o ABSOLUTE
    ../../../Core/Src/i2c.c                  0x00000000   Number         0  i2c.o ABSOLUTE
    ../../../Core/Src/quadspi.c              0x00000000   Number         0  quadspi.o ABSOLUTE
    ../../../Core/Src/sdmmc.c                0x00000000   Number         0  sdmmc.o ABSOLUTE
    ../../../Core/Src/spi.c                  0x00000000   Number         0  spi.o ABSOLUTE
    ../../../Core/Src/stm32l4xx_hal_msp.c    0x00000000   Number         0  stm32l4xx_hal_msp.o ABSOLUTE
    ../../../Core/Src/stm32l4xx_it.c         0x00000000   Number         0  stm32l4xx_it.o ABSOLUTE
    ../../../Core/Src/system_stm32l4xx.c     0x00000000   Number         0  system_stm32l4xx.o ABSOLUTE
    ../../../Core/Src/tim.c                  0x00000000   Number         0  tim.o ABSOLUTE
    ../../../Core/Src/usart.c                0x00000000   Number         0  usart.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_adc.c 0x00000000   Number         0  stm32l4xx_hal_adc.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_adc_ex.c 0x00000000   Number         0  stm32l4xx_hal_adc_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dac.c 0x00000000   Number         0  stm32l4xx_hal_dac.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dac_ex.c 0x00000000   Number         0  stm32l4xx_hal_dac_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_exti.c 0x00000000   Number         0  stm32l4xx_hal_exti.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_qspi.c 0x00000000   Number         0  stm32l4xx_hal_qspi.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_sd.c 0x00000000   Number         0  stm32l4xx_hal_sd.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_sd_ex.c 0x00000000   Number         0  stm32l4xx_hal_sd_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi.c 0x00000000   Number         0  stm32l4xx_hal_spi.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi_ex.c 0x00000000   Number         0  stm32l4xx_hal_spi_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart.c 0x00000000   Number         0  stm32l4xx_hal_uart.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart_ex.c 0x00000000   Number         0  stm32l4xx_hal_uart_ex.o ABSOLUTE
    ../../../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_ll_sdmmc.c 0x00000000   Number         0  stm32l4xx_ll_sdmmc.o ABSOLUTE
    ../../../Hardware\E53_IA1\E53_IA1.c      0x00000000   Number         0  e53_ia1.o ABSOLUTE
    ../../../Hardware\FATFS\App\fatfs.c      0x00000000   Number         0  fatfs.o ABSOLUTE
    ../../../Hardware\FATFS\Target\bsp_driver_sd.c 0x00000000   Number         0  bsp_driver_sd.o ABSOLUTE
    ../../../Hardware\FATFS\Target\sd_diskio.c 0x00000000   Number         0  sd_diskio.o ABSOLUTE
    ../../../Hardware\FLASH\flash.c          0x00000000   Number         0  flash.o ABSOLUTE
    ../../../Hardware\LCD\HzLib.c            0x00000000   Number         0  hzlib.o ABSOLUTE
    ../../../Hardware\LCD\lcd.c              0x00000000   Number         0  lcd.o ABSOLUTE
    ../../../Hardware\QSPI_FLASH\hal_qspi_flash.c 0x00000000   Number         0  hal_qspi_flash.o ABSOLUTE
    ../../../Middlewares\Third_Party\FatFs\src\diskio.c 0x00000000   Number         0  diskio.o ABSOLUTE
    ../../../Middlewares\Third_Party\FatFs\src\ff.c 0x00000000   Number         0  ff.o ABSOLUTE
    ../../../Middlewares\Third_Party\FatFs\src\ff_gen_drv.c 0x00000000   Number         0  ff_gen_drv.o ABSOLUTE
    ../../../Middlewares\Third_Party\FatFs\src\option\cc936.c 0x00000000   Number         0  cc936.o ABSOLUTE
    ../../../Middlewares\Third_Party\FatFs\src\option\syscall.c 0x00000000   Number         0  syscall.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../main.c                                0x00000000   Number         0  main.o ABSOLUTE
    ..\..\..\Core\Src\adc.c                  0x00000000   Number         0  adc.o ABSOLUTE
    ..\..\..\Core\Src\dac.c                  0x00000000   Number         0  dac.o ABSOLUTE
    ..\..\..\Core\Src\dma.c                  0x00000000   Number         0  dma.o ABSOLUTE
    ..\..\..\Core\Src\gpio.c                 0x00000000   Number         0  gpio.o ABSOLUTE
    ..\..\..\Core\Src\i2c.c                  0x00000000   Number         0  i2c.o ABSOLUTE
    ..\..\..\Core\Src\quadspi.c              0x00000000   Number         0  quadspi.o ABSOLUTE
    ..\..\..\Core\Src\sdmmc.c                0x00000000   Number         0  sdmmc.o ABSOLUTE
    ..\..\..\Core\Src\spi.c                  0x00000000   Number         0  spi.o ABSOLUTE
    ..\..\..\Core\Src\stm32l4xx_hal_msp.c    0x00000000   Number         0  stm32l4xx_hal_msp.o ABSOLUTE
    ..\..\..\Core\Src\stm32l4xx_it.c         0x00000000   Number         0  stm32l4xx_it.o ABSOLUTE
    ..\..\..\Core\Src\system_stm32l4xx.c     0x00000000   Number         0  system_stm32l4xx.o ABSOLUTE
    ..\..\..\Core\Src\tim.c                  0x00000000   Number         0  tim.o ABSOLUTE
    ..\..\..\Core\Src\usart.c                0x00000000   Number         0  usart.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_adc.c 0x00000000   Number         0  stm32l4xx_hal_adc.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_adc_ex.c 0x00000000   Number         0  stm32l4xx_hal_adc_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dac.c 0x00000000   Number         0  stm32l4xx_hal_dac.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dac_ex.c 0x00000000   Number         0  stm32l4xx_hal_dac_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_exti.c 0x00000000   Number         0  stm32l4xx_hal_exti.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_qspi.c 0x00000000   Number         0  stm32l4xx_hal_qspi.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_sd.c 0x00000000   Number         0  stm32l4xx_hal_sd.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_sd_ex.c 0x00000000   Number         0  stm32l4xx_hal_sd_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_spi.c 0x00000000   Number         0  stm32l4xx_hal_spi.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_spi_ex.c 0x00000000   Number         0  stm32l4xx_hal_spi_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_uart.c 0x00000000   Number         0  stm32l4xx_hal_uart.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_uart_ex.c 0x00000000   Number         0  stm32l4xx_hal_uart_ex.o ABSOLUTE
    ..\..\..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_ll_sdmmc.c 0x00000000   Number         0  stm32l4xx_ll_sdmmc.o ABSOLUTE
    ..\..\..\Hardware\\E53_IA1\\E53_IA1.c    0x00000000   Number         0  e53_ia1.o ABSOLUTE
    ..\..\..\Hardware\\FATFS\\App\\fatfs.c   0x00000000   Number         0  fatfs.o ABSOLUTE
    ..\..\..\Hardware\\FATFS\\Target\\bsp_driver_sd.c 0x00000000   Number         0  bsp_driver_sd.o ABSOLUTE
    ..\..\..\Hardware\\FATFS\\Target\\sd_diskio.c 0x00000000   Number         0  sd_diskio.o ABSOLUTE
    ..\..\..\Hardware\\FLASH\\flash.c        0x00000000   Number         0  flash.o ABSOLUTE
    ..\..\..\Hardware\\LCD\\lcd.c            0x00000000   Number         0  lcd.o ABSOLUTE
    ..\..\..\Hardware\\QSPI_FLASH\\hal_qspi_flash.c 0x00000000   Number         0  hal_qspi_flash.o ABSOLUTE
    ..\..\..\Middlewares\\Third_Party\\FatFs\\src\\diskio.c 0x00000000   Number         0  diskio.o ABSOLUTE
    ..\..\..\Middlewares\\Third_Party\\FatFs\\src\\ff.c 0x00000000   Number         0  ff.o ABSOLUTE
    ..\..\..\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c 0x00000000   Number         0  ff_gen_drv.o ABSOLUTE
    ..\..\..\Middlewares\\Third_Party\\FatFs\\src\\option\\cc936.c 0x00000000   Number         0  cc936.o ABSOLUTE
    ..\..\..\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c 0x00000000   Number         0  syscall.o ABSOLUTE
    ..\\myCode\\esp8266.c                    0x00000000   Number         0  esp8266.o ABSOLUTE
    ..\main.c                                0x00000000   Number         0  main.o ABSOLUTE
    ..\myCode\esp8266.c                      0x00000000   Number         0  esp8266.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32l431xx.s                    0x00000000   Number         0  startup_stm32l431xx.o ABSOLUTE
    RESET                                    0x08000000   Section      396  startup_stm32l431xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x0800018c   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x0800018c   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000190   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000194   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000194   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000194   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x0800019c   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001a0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001a0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001a0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001a0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a4   Section       36  startup_stm32l431xx.o(.text)
    $v0                                      0x080001a4   Number         0  startup_stm32l431xx.o(.text)
    .text                                    0x080001c8   Section        0  uldiv.o(.text)
    .text                                    0x08000230   Section        0  memseta.o(.text)
    .text                                    0x08000254   Section        0  strlen.o(.text)
    .text                                    0x08000262   Section        0  ddiv.o(.text)
    .text                                    0x08000340   Section        0  dflti.o(.text)
    .text                                    0x08000362   Section        0  f2d.o(.text)
    .text                                    0x08000388   Section        0  d2f.o(.text)
    .text                                    0x080003c0   Section        0  uidiv.o(.text)
    .text                                    0x080003ec   Section        0  llshl.o(.text)
    .text                                    0x0800040a   Section        0  llushr.o(.text)
    .text                                    0x0800042a   Section        0  fepilogue.o(.text)
    .text                                    0x0800042a   Section        0  iusefp.o(.text)
    .text                                    0x08000498   Section        0  depilogue.o(.text)
    .text                                    0x08000552   Section        0  dadd.o(.text)
    .text                                    0x080006b0   Section        0  dmul.o(.text)
    .text                                    0x0800079a   Section        0  dfixul.o(.text)
    .text                                    0x080007cc   Section       48  cdrcmple.o(.text)
    .text                                    0x080007fc   Section       36  init.o(.text)
    .text                                    0x08000820   Section        0  llsshr.o(.text)
    i.ADC1_IRQHandler                        0x08000844   Section        0  stm32l4xx_it.o(i.ADC1_IRQHandler)
    i.BSP_SD_AbortCallback                   0x08000850   Section        0  bsp_driver_sd.o(i.BSP_SD_AbortCallback)
    i.BSP_SD_ReadCpltCallback                0x08000852   Section        0  bsp_driver_sd.o(i.BSP_SD_ReadCpltCallback)
    i.BSP_SD_WriteCpltCallback               0x08000854   Section        0  bsp_driver_sd.o(i.BSP_SD_WriteCpltCallback)
    i.BusFault_Handler                       0x08000856   Section        0  stm32l4xx_it.o(i.BusFault_Handler)
    i.Convert_BH1750                         0x08000858   Section        0  e53_ia1.o(i.Convert_BH1750)
    i.DMA1_Channel1_IRQHandler               0x080008a8   Section        0  stm32l4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel4_IRQHandler               0x080008b4   Section        0  stm32l4xx_it.o(i.DMA1_Channel4_IRQHandler)
    i.DMA1_Channel5_IRQHandler               0x080008c0   Section        0  stm32l4xx_it.o(i.DMA1_Channel5_IRQHandler)
    i.DebugMon_Handler                       0x080008cc   Section        0  stm32l4xx_it.o(i.DebugMon_Handler)
    i.E53_IA1_Init                           0x080008ce   Section        0  e53_ia1.o(i.E53_IA1_Init)
    i.E53_IA1_Read_Data                      0x080008dc   Section        0  e53_ia1.o(i.E53_IA1_Read_Data)
    i.EXTI2_IRQHandler                       0x0800097c   Section        0  stm32l4xx_it.o(i.EXTI2_IRQHandler)
    i.EXTI3_IRQHandler                       0x08000982   Section        0  stm32l4xx_it.o(i.EXTI3_IRQHandler)
    i.Error_Handler                          0x08000988   Section        0  main.o(i.Error_Handler)
    i.HAL_ADCEx_EndOfSamplingCallback        0x0800098c   Section        0  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x0800098e   Section        0  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADCEx_InjectedQueueOverflowCallback 0x08000990   Section        0  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback)
    i.HAL_ADCEx_LevelOutOfWindow2Callback    0x08000992   Section        0  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback)
    i.HAL_ADCEx_LevelOutOfWindow3Callback    0x08000994   Section        0  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback)
    i.HAL_ADC_ConfigChannel                  0x08000998   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08000d8a   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08000d8c   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_IRQHandler                     0x08000d8e   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x08000f90   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x08001140   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x08001144   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_DAC_ConfigChannel                  0x080011e8   Section        0  stm32l4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_Init                           0x08001306   Section        0  stm32l4xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x08001330   Section        0  dac.o(i.HAL_DAC_MspInit)
    i.HAL_DMA_Abort                          0x0800138c   Section        0  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080013d6   Section        0  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08001424   Section        0  stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x080014e0   Section        0  stm32l4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_Delay                              0x080015a0   Section        0  stm32l4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x080015c4   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080015c8   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x080015e0   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x080017b6   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080017c0   Section        0  stm32l4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x080017cc   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08001822   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x08001874   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Master_Receive                 0x0800192c   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    i.HAL_I2C_Master_Transmit                0x08001a58   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MspInit                        0x08001b84   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08001c7c   Section        0  stm32l4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001c8c   Section        0  stm32l4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001cac   Section        0  stm32l4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001cf0   Section        0  stm32l4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001d1c   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001d38   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001d78   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x08001d9c   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_GetVoltageRange              0x08001e04   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    i.HAL_PWR_EnableBkUpAccess               0x08001e14   Section        0  stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess)
    i.HAL_QSPI_Init                          0x08001e24   Section        0  stm32l4xx_hal_qspi.o(i.HAL_QSPI_Init)
    i.HAL_QSPI_MspInit                       0x08001ecc   Section        0  quadspi.o(i.HAL_QSPI_MspInit)
    i.HAL_RCCEx_EnableMSIPLLMode             0x08001f34   Section        0  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableMSIPLLMode)
    i.HAL_RCCEx_GetPeriphCLKFreq             0x08001f44   Section        0  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq)
    i.HAL_RCCEx_PeriphCLKConfig              0x080022a0   Section        0  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x0800255c   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080026a0   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080026c4   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080026e8   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800278c   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SD_AbortCallback                   0x08002cec   Section        0  bsp_driver_sd.o(i.HAL_SD_AbortCallback)
    i.HAL_SD_ErrorCallback                   0x08002cf4   Section        0  stm32l4xx_hal_sd.o(i.HAL_SD_ErrorCallback)
    i.HAL_SD_GetCardCSD                      0x08002cf8   Section        0  stm32l4xx_hal_sd.o(i.HAL_SD_GetCardCSD)
    i.HAL_SD_GetCardState                    0x08002e90   Section        0  stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState)
    i.HAL_SD_IRQHandler                      0x08002ebc   Section        0  stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler)
    i.HAL_SD_Init                            0x080030b4   Section        0  stm32l4xx_hal_sd.o(i.HAL_SD_Init)
    i.HAL_SD_InitCard                        0x080030ec   Section        0  stm32l4xx_hal_sd.o(i.HAL_SD_InitCard)
    i.HAL_SD_MspInit                         0x080031b4   Section        0  sdmmc.o(i.HAL_SD_MspInit)
    i.HAL_SD_RxCpltCallback                  0x08003248   Section        0  bsp_driver_sd.o(i.HAL_SD_RxCpltCallback)
    i.HAL_SD_TxCpltCallback                  0x08003250   Section        0  bsp_driver_sd.o(i.HAL_SD_TxCpltCallback)
    i.HAL_SPI_Init                           0x08003258   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08003338   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_Transmit                       0x08003484   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit)
    i.HAL_SYSTICK_Config                     0x08003612   Section        0  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_Break2Callback               0x0800363a   Section        0  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    i.HAL_TIMEx_BreakCallback                0x0800363c   Section        0  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x0800363e   Section        0  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003640   Section        0  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIM_Base_Init                      0x080036c4   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003728   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_IC_CaptureCallback             0x080037b4   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080037b6   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x0800393a   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x0800393c   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08003a60   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08003ac2   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08003ac4   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08003ac6   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08003ac8   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_IT            0x08003aca   Section        0  stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT)
    i.HAL_UARTEx_RxEventCallback             0x08003b1c   Section        0  main.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_WakeupCallback              0x08003b70   Section        0  stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x08003b72   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08003b74   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08003e08   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003e70   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x0800408a   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x0800408c   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004142   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004144   Section        0  stm32l4xx_it.o(i.HardFault_Handler)
    i.I2C_Flush_TXDR                         0x08004146   Section        0  stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    I2C_Flush_TXDR                           0x08004147   Thumb Code    34  stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    i.I2C_IsErrorOccurred                    0x08004168   Section        0  stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    I2C_IsErrorOccurred                      0x08004169   Thumb Code   284  stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    i.I2C_TransferConfig                     0x08004288   Section        0  stm32l4xx_hal_i2c.o(i.I2C_TransferConfig)
    I2C_TransferConfig                       0x08004289   Thumb Code    44  stm32l4xx_hal_i2c.o(i.I2C_TransferConfig)
    i.I2C_WaitOnFlagUntilTimeout             0x080042b8   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x080042b9   Thumb Code    88  stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08004310   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08004311   Thumb Code   150  stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnSTOPFlagUntilTimeout         0x080043ac   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    I2C_WaitOnSTOPFlagUntilTimeout           0x080043ad   Thumb Code    76  stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    i.I2C_WaitOnTXISFlagUntilTimeout         0x080043f8   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    I2C_WaitOnTXISFlagUntilTimeout           0x080043f9   Thumb Code    80  stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    i.Init_BH1750                            0x08004448   Section        0  e53_ia1.o(i.Init_BH1750)
    i.Init_SHT30                             0x08004468   Section        0  e53_ia1.o(i.Init_SHT30)
    i.LCD_Address_Set                        0x0800448c   Section        0  lcd.o(i.LCD_Address_Set)
    i.LCD_Clear                              0x080044e0   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_Init                               0x08004538   Section        0  lcd.o(i.LCD_Init)
    i.LCD_SPI_Send                           0x08004704   Section        0  lcd.o(i.LCD_SPI_Send)
    LCD_SPI_Send                             0x08004705   Thumb Code     4  lcd.o(i.LCD_SPI_Send)
    i.LCD_ShowChar                           0x08004708   Section        0  lcd.o(i.LCD_ShowChar)
    i.LCD_ShowString                         0x0800484a   Section        0  lcd.o(i.LCD_ShowString)
    i.LCD_Write_Cmd                          0x08004898   Section        0  lcd.o(i.LCD_Write_Cmd)
    LCD_Write_Cmd                            0x08004899   Thumb Code    22  lcd.o(i.LCD_Write_Cmd)
    i.LCD_Write_Data                         0x080048b4   Section        0  lcd.o(i.LCD_Write_Data)
    LCD_Write_Data                           0x080048b5   Thumb Code    22  lcd.o(i.LCD_Write_Data)
    i.LCD_Write_HalfWord                     0x080048d0   Section        0  lcd.o(i.LCD_Write_HalfWord)
    i.LL_ADC_GetOffsetChannel                0x080048f8   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    LL_ADC_GetOffsetChannel                  0x080048f9   Thumb Code    12  stm32l4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    i.LL_ADC_INJ_IsConversionOngoing         0x08004904   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    LL_ADC_INJ_IsConversionOngoing           0x08004905   Thumb Code     8  stm32l4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    i.LL_ADC_REG_IsConversionOngoing         0x0800490c   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x0800490d   Thumb Code     8  stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsTriggerSourceSWStart      0x08004914   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    LL_ADC_REG_IsTriggerSourceSWStart        0x08004915   Thumb Code    16  stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    i.LL_ADC_SetChannelSamplingTime          0x08004924   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    LL_ADC_SetChannelSamplingTime            0x08004925   Thumb Code    30  stm32l4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    i.LL_ADC_SetOffsetState                  0x08004942   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    LL_ADC_SetOffsetState                    0x08004943   Thumb Code    16  stm32l4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    i.LPUART1_IRQHandler                     0x08004954   Section        0  stm32l4xx_it.o(i.LPUART1_IRQHandler)
    i.MX_ADC1_Init                           0x08004960   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DAC1_Init                           0x080049d8   Section        0  dac.o(i.MX_DAC1_Init)
    i.MX_DMA_Init                            0x08004a20   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08004a6c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08004b8c   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C3_Init                           0x08004be4   Section        0  i2c.o(i.MX_I2C3_Init)
    i.MX_LPUART1_UART_Init                   0x08004c3c   Section        0  usart.o(i.MX_LPUART1_UART_Init)
    i.MX_QUADSPI_Init                        0x08004c78   Section        0  quadspi.o(i.MX_QUADSPI_Init)
    i.MX_SDMMC1_SD_Init                      0x08004cb8   Section        0  sdmmc.o(i.MX_SDMMC1_SD_Init)
    i.MX_SPI1_Init                           0x08004cdc   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_SPI2_Init                           0x08004d28   Section        0  spi.o(i.MX_SPI2_Init)
    i.MX_SPI3_Init                           0x08004d78   Section        0  spi.o(i.MX_SPI3_Init)
    i.MX_TIM16_Init                          0x08004dc4   Section        0  tim.o(i.MX_TIM16_Init)
    i.MX_USART1_UART_Init                    0x08004e60   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004e9c   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08004ed8   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08004f14   Section        0  stm32l4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004f16   Section        0  stm32l4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08004f18   Section        0  stm32l4xx_it.o(i.PendSV_Handler)
    i.PeriphCommonClock_Config               0x08004f1a   Section        0  main.o(i.PeriphCommonClock_Config)
    i.QSPI_WaitFlagStateUntilTimeout         0x08004f60   Section        0  stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout)
    QSPI_WaitFlagStateUntilTimeout           0x08004f61   Thumb Code    72  stm32l4xx_hal_qspi.o(i.QSPI_WaitFlagStateUntilTimeout)
    i.RCCEx_GetSAIxPeriphCLKFreq             0x08004fa8   Section        0  stm32l4xx_hal_rcc_ex.o(i.RCCEx_GetSAIxPeriphCLKFreq)
    RCCEx_GetSAIxPeriphCLKFreq               0x08004fa9   Thumb Code   154  stm32l4xx_hal_rcc_ex.o(i.RCCEx_GetSAIxPeriphCLKFreq)
    i.RCCEx_PLLSAI1_Config                   0x08005050   Section        0  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    RCCEx_PLLSAI1_Config                     0x08005051   Thumb Code   278  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    i.RCC_SetFlashLatencyFromMSIRange        0x0800517c   Section        0  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    RCC_SetFlashLatencyFromMSIRange          0x0800517d   Thumb Code   116  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    i.SDMMC1_IRQHandler                      0x080051f8   Section        0  stm32l4xx_it.o(i.SDMMC1_IRQHandler)
    i.SDMMC_CmdAppCommand                    0x08005204   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand)
    i.SDMMC_CmdAppOperCommand                0x08005238   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand)
    i.SDMMC_CmdBlockLength                   0x0800526c   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength)
    i.SDMMC_CmdGoIdleState                   0x080052a0   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState)
    i.SDMMC_CmdOperCond                      0x080052f0   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond)
    i.SDMMC_CmdSelDesel                      0x08005320   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel)
    i.SDMMC_CmdSendCID                       0x08005352   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID)
    i.SDMMC_CmdSendCSD                       0x0800537e   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD)
    i.SDMMC_CmdSendStatus                    0x080053aa   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus)
    i.SDMMC_CmdSetRelAdd                     0x080053dc   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd)
    i.SDMMC_CmdStopTransfer                  0x0800540c   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer)
    i.SDMMC_GetCmdResp1                      0x08005440   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1)
    i.SDMMC_GetCmdResp2                      0x08005560   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2)
    i.SDMMC_GetCmdResp3                      0x080055b0   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3)
    i.SDMMC_GetCmdResp6                      0x080055f4   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6)
    i.SDMMC_GetCmdResp7                      0x0800567c   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7)
    i.SDMMC_GetPowerState                    0x080056d4   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetPowerState)
    i.SDMMC_GetResponse                      0x080056dc   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse)
    i.SDMMC_Init                             0x080056e2   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_Init)
    i.SDMMC_PowerState_ON                    0x08005704   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_PowerState_ON)
    i.SDMMC_ReadFIFO                         0x08005714   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_ReadFIFO)
    i.SDMMC_SendCommand                      0x0800571a   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand)
    i.SDMMC_WriteFIFO                        0x0800573a   Section        0  stm32l4xx_ll_sdmmc.o(i.SDMMC_WriteFIFO)
    i.SD_DMARxAbort                          0x08005744   Section        0  stm32l4xx_hal_sd.o(i.SD_DMARxAbort)
    SD_DMARxAbort                            0x08005745   Thumb Code    68  stm32l4xx_hal_sd.o(i.SD_DMARxAbort)
    i.SD_DMATxAbort                          0x08005788   Section        0  stm32l4xx_hal_sd.o(i.SD_DMATxAbort)
    SD_DMATxAbort                            0x08005789   Thumb Code    68  stm32l4xx_hal_sd.o(i.SD_DMATxAbort)
    i.SD_InitCard                            0x080057cc   Section        0  stm32l4xx_hal_sd.o(i.SD_InitCard)
    SD_InitCard                              0x080057cd   Thumb Code   238  stm32l4xx_hal_sd.o(i.SD_InitCard)
    i.SD_PowerON                             0x080058bc   Section        0  stm32l4xx_hal_sd.o(i.SD_PowerON)
    SD_PowerON                               0x080058bd   Thumb Code   174  stm32l4xx_hal_sd.o(i.SD_PowerON)
    i.SHT3x_CalcRH                           0x08005978   Section        0  e53_ia1.o(i.SHT3x_CalcRH)
    i.SHT3x_CalcTemperatureC                 0x080059a0   Section        0  e53_ia1.o(i.SHT3x_CalcTemperatureC)
    i.SHT3x_CheckCrc                         0x080059d4   Section        0  e53_ia1.o(i.SHT3x_CheckCrc)
    i.SPI2_WriteByte                         0x08005a10   Section        0  spi.o(i.SPI2_WriteByte)
    i.SPI_EndRxTxTransaction                 0x08005a20   Section        0  stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x08005a21   Thumb Code    72  stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFifoStateUntilTimeout          0x08005a68   Section        0  stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout)
    SPI_WaitFifoStateUntilTimeout            0x08005a69   Thumb Code   218  stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout)
    i.SPI_WaitFlagStateUntilTimeout          0x08005b50   Section        0  stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x08005b51   Thumb Code   180  stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SVC_Handler                            0x08005c08   Section        0  stm32l4xx_it.o(i.SVC_Handler)
    i.Start_BH1750                           0x08005c0c   Section        0  e53_ia1.o(i.Start_BH1750)
    i.SysTick_Handler                        0x08005c2c   Section        0  stm32l4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005c30   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005cc4   Section        0  system_stm32l4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08005cd4   Section        0  stm32l4xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x08005ce0   Section        0  stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_OC1_SetConfig                      0x08005d4c   Section        0  stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08005d4d   Thumb Code   104  stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08005dc4   Section        0  stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08005e3c   Section        0  stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08005e3d   Thumb Code   100  stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08005eb0   Section        0  stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08005eb1   Thumb Code    80  stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_OC5_SetConfig                      0x08005f10   Section        0  stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    TIM_OC5_SetConfig                        0x08005f11   Thumb Code    74  stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    i.TIM_OC6_SetConfig                      0x08005f6c   Section        0  stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    TIM_OC6_SetConfig                        0x08005f6d   Thumb Code    76  stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    i.UART_AdvFeatureConfig                  0x08005fc8   Section        0  stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08006090   Section        0  stm32l4xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x080060ec   Section        0  stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080060ed   Thumb Code    20  stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08006100   Section        0  stm32l4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006101   Thumb Code    74  stm32l4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_RxISR_16BIT                       0x0800614a   Section        0  stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT)
    UART_RxISR_16BIT                         0x0800614b   Thumb Code   158  stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT)
    i.UART_RxISR_8BIT                        0x080061e8   Section        0  stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT)
    UART_RxISR_8BIT                          0x080061e9   Thumb Code   158  stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT)
    i.UART_SetConfig                         0x08006288   Section        0  stm32l4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08006488   Section        0  stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08006540   Section        0  stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08006608   Section        0  stm32l4xx_it.o(i.USART1_IRQHandler)
    i.USART3_IRQHandler                      0x08006614   Section        0  stm32l4xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08006620   Section        0  stm32l4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x08006624   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x08006644   Section        0  printfa.o(i.__0sprintf)
    i.__0vsnprintf                           0x0800666c   Section        0  printfa.o(i.__0vsnprintf)
    i.__NVIC_SetPriority                     0x080066a0   Section        0  stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080066a1   Thumb Code    32  stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x080066c0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080066ce   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080066d0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080066e0   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080066e1   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x0800686c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800686d   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08006f26   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08006f27   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08006f4a   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08006f4b   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08006f78   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08006f79   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x08006f8e   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08006f8f   Thumb Code    10  printfa.o(i._sputc)
    i.esp8266_init                           0x08006f98   Section        0  esp8266.o(i.esp8266_init)
    i.esp8266_publish                        0x08007140   Section        0  esp8266.o(i.esp8266_publish)
    i.fputc                                  0x080071d4   Section        0  usart.o(i.fputc)
    i.main                                   0x080071ec   Section        0  main.o(i.main)
    i.uart_print                             0x08007488   Section        0  usart.o(i.uart_print)
    .constdata                               0x080074c0   Section       64  system_stm32l4xx.o(.constdata)
    .constdata                               0x08007500   Section        8  system_stm32l4xx.o(.constdata)
    .constdata                               0x08007508   Section    19380  lcd.o(.constdata)
    .data                                    0x20000000   Section       12  stm32l4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32l4xx.o(.data)
    .data                                    0x20000010   Section        4  lcd.o(.data)
    .data                                    0x20000014   Section        4  stdout.o(.data)
    .bss                                     0x20000018   Section      128  main.o(.bss)
    .bss                                     0x20000098   Section      172  adc.o(.bss)
    .bss                                     0x20000144   Section       20  dac.o(.bss)
    .bss                                     0x20000158   Section      168  i2c.o(.bss)
    .bss                                     0x20000200   Section      672  usart.o(.bss)
    .bss                                     0x200004a0   Section       76  quadspi.o(.bss)
    .bss                                     0x200004ec   Section      132  sdmmc.o(.bss)
    .bss                                     0x20000570   Section      300  spi.o(.bss)
    .bss                                     0x2000069c   Section      152  tim.o(.bss)
    .bss                                     0x20000734   Section     1152  lcd.o(.bss)
    lcd_buf                                  0x20000734   Data        1152  lcd.o(.bss)
    STACK                                    0x20000bb8   Section     1024  startup_stm32l431xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x0000018c   Number         0  startup_stm32l431xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32l431xx.o(RESET)
    __Vectors_End                            0x0800018c   Data           0  startup_stm32l431xx.o(RESET)
    __main                                   0x0800018d   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x0800018d   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000195   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000195   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000195   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000195   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x0800019d   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001a1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001a1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a5   Thumb Code     8  startup_stm32l431xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    COMP_IRQHandler                          0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    CRS_IRQHandler                           0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel7_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI0_IRQHandler                         0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI1_IRQHandler                         0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI4_IRQHandler                         0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    FLASH_IRQHandler                         0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    FPU_IRQHandler                           0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    LPTIM1_IRQHandler                        0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    LPTIM2_IRQHandler                        0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    PVD_PVM_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    QUADSPI_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    RCC_IRQHandler                           0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    RNG_IRQHandler                           0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    SAI1_IRQHandler                          0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    SPI1_IRQHandler                          0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    SPI2_IRQHandler                          0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    SPI3_IRQHandler                          0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    SWPMI1_IRQHandler                        0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM7_IRQHandler                          0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    TSC_IRQHandler                           0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    USART2_IRQHandler                        0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    WWDG_IRQHandler                          0x080001bf   Thumb Code     0  startup_stm32l431xx.o(.text)
    __aeabi_uldivmod                         0x080001c9   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000231   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000231   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000231   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800023f   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800023f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800023f   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000243   Thumb Code    18  memseta.o(.text)
    strlen                                   0x08000255   Thumb Code    14  strlen.o(.text)
    __aeabi_ddiv                             0x08000263   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x08000341   Thumb Code    34  dflti.o(.text)
    __aeabi_f2d                              0x08000363   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000389   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x080003c1   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080003c1   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080003ed   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080003ed   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800040b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800040b   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x0800042b   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800042b   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800043d   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000499   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080004b7   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000553   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000695   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800069b   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080006b1   Thumb Code   228  dmul.o(.text)
    __aeabi_d2ulz                            0x0800079b   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080007cd   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080007fd   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080007fd   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000821   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000821   Thumb Code     0  llsshr.o(.text)
    ADC1_IRQHandler                          0x08000845   Thumb Code     6  stm32l4xx_it.o(i.ADC1_IRQHandler)
    BSP_SD_AbortCallback                     0x08000851   Thumb Code     2  bsp_driver_sd.o(i.BSP_SD_AbortCallback)
    BSP_SD_ReadCpltCallback                  0x08000853   Thumb Code     2  bsp_driver_sd.o(i.BSP_SD_ReadCpltCallback)
    BSP_SD_WriteCpltCallback                 0x08000855   Thumb Code     2  bsp_driver_sd.o(i.BSP_SD_WriteCpltCallback)
    BusFault_Handler                         0x08000857   Thumb Code     2  stm32l4xx_it.o(i.BusFault_Handler)
    Convert_BH1750                           0x08000859   Thumb Code    66  e53_ia1.o(i.Convert_BH1750)
    DMA1_Channel1_IRQHandler                 0x080008a9   Thumb Code     6  stm32l4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel4_IRQHandler                 0x080008b5   Thumb Code     6  stm32l4xx_it.o(i.DMA1_Channel4_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x080008c1   Thumb Code     6  stm32l4xx_it.o(i.DMA1_Channel5_IRQHandler)
    DebugMon_Handler                         0x080008cd   Thumb Code     2  stm32l4xx_it.o(i.DebugMon_Handler)
    E53_IA1_Init                             0x080008cf   Thumb Code    14  e53_ia1.o(i.E53_IA1_Init)
    E53_IA1_Read_Data                        0x080008dd   Thumb Code   152  e53_ia1.o(i.E53_IA1_Read_Data)
    EXTI2_IRQHandler                         0x0800097d   Thumb Code     6  stm32l4xx_it.o(i.EXTI2_IRQHandler)
    EXTI3_IRQHandler                         0x08000983   Thumb Code     6  stm32l4xx_it.o(i.EXTI3_IRQHandler)
    Error_Handler                            0x08000989   Thumb Code     4  main.o(i.Error_Handler)
    HAL_ADCEx_EndOfSamplingCallback          0x0800098d   Thumb Code     2  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback)
    HAL_ADCEx_InjectedConvCpltCallback       0x0800098f   Thumb Code     2  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADCEx_InjectedQueueOverflowCallback  0x08000991   Thumb Code     2  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback)
    HAL_ADCEx_LevelOutOfWindow2Callback      0x08000993   Thumb Code     2  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback)
    HAL_ADCEx_LevelOutOfWindow3Callback      0x08000995   Thumb Code     2  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback)
    HAL_ADC_ConfigChannel                    0x08000999   Thumb Code   960  stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08000d8b   Thumb Code     2  stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ErrorCallback                    0x08000d8d   Thumb Code     2  stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x08000d8f   Thumb Code   514  stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08000f91   Thumb Code   400  stm32l4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x08001141   Thumb Code     2  stm32l4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x08001145   Thumb Code   144  adc.o(i.HAL_ADC_MspInit)
    HAL_DAC_ConfigChannel                    0x080011e9   Thumb Code   286  stm32l4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_Init                             0x08001307   Thumb Code    40  stm32l4xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x08001331   Thumb Code    82  dac.o(i.HAL_DAC_MspInit)
    HAL_DMA_Abort                            0x0800138d   Thumb Code    74  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080013d7   Thumb Code    78  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001425   Thumb Code   186  stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080014e1   Thumb Code   178  stm32l4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_Delay                                0x080015a1   Thumb Code    32  stm32l4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x080015c5   Thumb Code     2  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080015c9   Thumb Code    18  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x080015e1   Thumb Code   434  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x080017b7   Thumb Code    10  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080017c1   Thumb Code     6  stm32l4xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x080017cd   Thumb Code    86  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08001823   Thumb Code    82  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x08001875   Thumb Code   178  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Master_Receive                   0x0800192d   Thumb Code   282  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    HAL_I2C_Master_Transmit                  0x08001a59   Thumb Code   282  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MspInit                          0x08001b85   Thumb Code   232  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08001c7d   Thumb Code    12  stm32l4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001c8d   Thumb Code    30  stm32l4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001cad   Thumb Code    58  stm32l4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001cf1   Thumb Code    38  stm32l4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001d1d   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001d39   Thumb Code    60  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001d79   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x08001d9d   Thumb Code    90  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_GetVoltageRange                0x08001e05   Thumb Code    10  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    HAL_PWR_EnableBkUpAccess                 0x08001e15   Thumb Code    12  stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess)
    HAL_QSPI_Init                            0x08001e25   Thumb Code   158  stm32l4xx_hal_qspi.o(i.HAL_QSPI_Init)
    HAL_QSPI_MspInit                         0x08001ecd   Thumb Code    90  quadspi.o(i.HAL_QSPI_MspInit)
    HAL_RCCEx_EnableMSIPLLMode               0x08001f35   Thumb Code    12  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableMSIPLLMode)
    HAL_RCCEx_GetPeriphCLKFreq               0x08001f45   Thumb Code   786  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq)
    HAL_RCCEx_PeriphCLKConfig                0x080022a1   Thumb Code   682  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x0800255d   Thumb Code   304  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080026a1   Thumb Code    24  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080026c5   Thumb Code    24  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080026e9   Thumb Code   150  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800278d   Thumb Code  1366  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SD_AbortCallback                     0x08002ced   Thumb Code     8  bsp_driver_sd.o(i.HAL_SD_AbortCallback)
    HAL_SD_ErrorCallback                     0x08002cf5   Thumb Code     2  stm32l4xx_hal_sd.o(i.HAL_SD_ErrorCallback)
    HAL_SD_GetCardCSD                        0x08002cf9   Thumb Code   402  stm32l4xx_hal_sd.o(i.HAL_SD_GetCardCSD)
    HAL_SD_GetCardState                      0x08002e91   Thumb Code    44  stm32l4xx_hal_sd.o(i.HAL_SD_GetCardState)
    HAL_SD_IRQHandler                        0x08002ebd   Thumb Code   496  stm32l4xx_hal_sd.o(i.HAL_SD_IRQHandler)
    HAL_SD_Init                              0x080030b5   Thumb Code    54  stm32l4xx_hal_sd.o(i.HAL_SD_Init)
    HAL_SD_InitCard                          0x080030ed   Thumb Code   178  stm32l4xx_hal_sd.o(i.HAL_SD_InitCard)
    HAL_SD_MspInit                           0x080031b5   Thumb Code   130  sdmmc.o(i.HAL_SD_MspInit)
    HAL_SD_RxCpltCallback                    0x08003249   Thumb Code     8  bsp_driver_sd.o(i.HAL_SD_RxCpltCallback)
    HAL_SD_TxCpltCallback                    0x08003251   Thumb Code     8  bsp_driver_sd.o(i.HAL_SD_TxCpltCallback)
    HAL_SPI_Init                             0x08003259   Thumb Code   224  stm32l4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08003339   Thumb Code   308  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_Transmit                         0x08003485   Thumb Code   392  stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit)
    HAL_SYSTICK_Config                       0x08003613   Thumb Code    40  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_Break2Callback                 0x0800363b   Thumb Code     2  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    HAL_TIMEx_BreakCallback                  0x0800363d   Thumb Code     2  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x0800363f   Thumb Code     2  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003641   Thumb Code   126  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIM_Base_Init                        0x080036c5   Thumb Code    98  stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003729   Thumb Code   126  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_IC_CaptureCallback               0x080037b5   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080037b7   Thumb Code   388  stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x0800393b   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x0800393d   Thumb Code   292  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003a61   Thumb Code    98  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003ac3   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003ac5   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08003ac7   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08003ac9   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_IT              0x08003acb   Thumb Code    80  stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT)
    HAL_UARTEx_RxEventCallback               0x08003b1d   Thumb Code    48  main.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_WakeupCallback                0x08003b71   Thumb Code     2  stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x08003b73   Thumb Code     2  stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003b75   Thumb Code   652  stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08003e09   Thumb Code   102  stm32l4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003e71   Thumb Code   498  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x0800408b   Thumb Code     2  stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x0800408d   Thumb Code   182  stm32l4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004143   Thumb Code     2  stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004145   Thumb Code     2  stm32l4xx_it.o(i.HardFault_Handler)
    Init_BH1750                              0x08004449   Thumb Code    26  e53_ia1.o(i.Init_BH1750)
    Init_SHT30                               0x08004469   Thumb Code    26  e53_ia1.o(i.Init_SHT30)
    LCD_Address_Set                          0x0800448d   Thumb Code    82  lcd.o(i.LCD_Address_Set)
    LCD_Clear                                0x080044e1   Thumb Code    80  lcd.o(i.LCD_Clear)
    LCD_Init                                 0x08004539   Thumb Code   450  lcd.o(i.LCD_Init)
    LCD_ShowChar                             0x08004709   Thumb Code   300  lcd.o(i.LCD_ShowChar)
    LCD_ShowString                           0x0800484b   Thumb Code    76  lcd.o(i.LCD_ShowString)
    LCD_Write_HalfWord                       0x080048d1   Thumb Code    36  lcd.o(i.LCD_Write_HalfWord)
    LPUART1_IRQHandler                       0x08004955   Thumb Code     6  stm32l4xx_it.o(i.LPUART1_IRQHandler)
    MX_ADC1_Init                             0x08004961   Thumb Code   108  adc.o(i.MX_ADC1_Init)
    MX_DAC1_Init                             0x080049d9   Thumb Code    64  dac.o(i.MX_DAC1_Init)
    MX_DMA_Init                              0x08004a21   Thumb Code    72  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08004a6d   Thumb Code   276  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08004b8d   Thumb Code    76  i2c.o(i.MX_I2C1_Init)
    MX_I2C3_Init                             0x08004be5   Thumb Code    76  i2c.o(i.MX_I2C3_Init)
    MX_LPUART1_UART_Init                     0x08004c3d   Thumb Code    50  usart.o(i.MX_LPUART1_UART_Init)
    MX_QUADSPI_Init                          0x08004c79   Thumb Code    54  quadspi.o(i.MX_QUADSPI_Init)
    MX_SDMMC1_SD_Init                        0x08004cb9   Thumb Code    26  sdmmc.o(i.MX_SDMMC1_SD_Init)
    MX_SPI1_Init                             0x08004cdd   Thumb Code    66  spi.o(i.MX_SPI1_Init)
    MX_SPI2_Init                             0x08004d29   Thumb Code    70  spi.o(i.MX_SPI2_Init)
    MX_SPI3_Init                             0x08004d79   Thumb Code    66  spi.o(i.MX_SPI3_Init)
    MX_TIM16_Init                            0x08004dc5   Thumb Code   146  tim.o(i.MX_TIM16_Init)
    MX_USART1_UART_Init                      0x08004e61   Thumb Code    52  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004e9d   Thumb Code    52  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004ed9   Thumb Code    52  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08004f15   Thumb Code     2  stm32l4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004f17   Thumb Code     2  stm32l4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08004f19   Thumb Code     2  stm32l4xx_it.o(i.PendSV_Handler)
    PeriphCommonClock_Config                 0x08004f1b   Thumb Code    70  main.o(i.PeriphCommonClock_Config)
    SDMMC1_IRQHandler                        0x080051f9   Thumb Code     6  stm32l4xx_it.o(i.SDMMC1_IRQHandler)
    SDMMC_CmdAppCommand                      0x08005205   Thumb Code    50  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand)
    SDMMC_CmdAppOperCommand                  0x08005239   Thumb Code    48  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand)
    SDMMC_CmdBlockLength                     0x0800526d   Thumb Code    50  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength)
    SDMMC_CmdGoIdleState                     0x080052a1   Thumb Code    74  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState)
    SDMMC_CmdOperCond                        0x080052f1   Thumb Code    48  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond)
    SDMMC_CmdSelDesel                        0x08005321   Thumb Code    50  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel)
    SDMMC_CmdSendCID                         0x08005353   Thumb Code    44  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID)
    SDMMC_CmdSendCSD                         0x0800537f   Thumb Code    44  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD)
    SDMMC_CmdSendStatus                      0x080053ab   Thumb Code    50  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus)
    SDMMC_CmdSetRelAdd                       0x080053dd   Thumb Code    48  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd)
    SDMMC_CmdStopTransfer                    0x0800540d   Thumb Code    46  stm32l4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer)
    SDMMC_GetCmdResp1                        0x08005441   Thumb Code   278  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1)
    SDMMC_GetCmdResp2                        0x08005561   Thumb Code    76  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2)
    SDMMC_GetCmdResp3                        0x080055b1   Thumb Code    62  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3)
    SDMMC_GetCmdResp6                        0x080055f5   Thumb Code   130  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6)
    SDMMC_GetCmdResp7                        0x0800567d   Thumb Code    82  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7)
    SDMMC_GetPowerState                      0x080056d5   Thumb Code     8  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetPowerState)
    SDMMC_GetResponse                        0x080056dd   Thumb Code     6  stm32l4xx_ll_sdmmc.o(i.SDMMC_GetResponse)
    SDMMC_Init                               0x080056e3   Thumb Code    34  stm32l4xx_ll_sdmmc.o(i.SDMMC_Init)
    SDMMC_PowerState_ON                      0x08005705   Thumb Code    16  stm32l4xx_ll_sdmmc.o(i.SDMMC_PowerState_ON)
    SDMMC_ReadFIFO                           0x08005715   Thumb Code     6  stm32l4xx_ll_sdmmc.o(i.SDMMC_ReadFIFO)
    SDMMC_SendCommand                        0x0800571b   Thumb Code    32  stm32l4xx_ll_sdmmc.o(i.SDMMC_SendCommand)
    SDMMC_WriteFIFO                          0x0800573b   Thumb Code    10  stm32l4xx_ll_sdmmc.o(i.SDMMC_WriteFIFO)
    SHT3x_CalcRH                             0x08005979   Thumb Code    30  e53_ia1.o(i.SHT3x_CalcRH)
    SHT3x_CalcTemperatureC                   0x080059a1   Thumb Code    38  e53_ia1.o(i.SHT3x_CalcTemperatureC)
    SHT3x_CheckCrc                           0x080059d5   Thumb Code    58  e53_ia1.o(i.SHT3x_CheckCrc)
    SPI2_WriteByte                           0x08005a11   Thumb Code    12  spi.o(i.SPI2_WriteByte)
    SVC_Handler                              0x08005c09   Thumb Code     2  stm32l4xx_it.o(i.SVC_Handler)
    Start_BH1750                             0x08005c0d   Thumb Code    26  e53_ia1.o(i.Start_BH1750)
    SysTick_Handler                          0x08005c2d   Thumb Code     4  stm32l4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005c31   Thumb Code   144  main.o(i.SystemClock_Config)
    SystemInit                               0x08005cc5   Thumb Code    12  system_stm32l4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x08005cd5   Thumb Code     6  stm32l4xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x08005ce1   Thumb Code    94  stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_OC2_SetConfig                        0x08005dc5   Thumb Code   102  stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_AdvFeatureConfig                    0x08005fc9   Thumb Code   200  stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08006091   Thumb Code    92  stm32l4xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08006289   Thumb Code   478  stm32l4xx_hal_uart.o(i.UART_SetConfig)
    UART_Start_Receive_IT                    0x08006489   Thumb Code   176  stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT)
    UART_WaitOnFlagUntilTimeout              0x08006541   Thumb Code   198  stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x08006609   Thumb Code     6  stm32l4xx_it.o(i.USART1_IRQHandler)
    USART3_IRQHandler                        0x08006615   Thumb Code     6  stm32l4xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08006621   Thumb Code     2  stm32l4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x08006625   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08006625   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08006625   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08006625   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08006625   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x08006645   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08006645   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08006645   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08006645   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08006645   Thumb Code     0  printfa.o(i.__0sprintf)
    __0vsnprintf                             0x0800666d   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x0800666d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x0800666d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x0800666d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x0800666d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __scatterload_copy                       0x080066c1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080066cf   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080066d1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    esp8266_init                             0x08006f99   Thumb Code   144  esp8266.o(i.esp8266_init)
    esp8266_publish                          0x08007141   Thumb Code    84  esp8266.o(i.esp8266_publish)
    fputc                                    0x080071d5   Thumb Code    20  usart.o(i.fputc)
    main                                     0x080071ed   Thumb Code   392  main.o(i.main)
    uart_print                               0x08007489   Thumb Code    56  usart.o(i.uart_print)
    AHBPrescTable                            0x080074c0   Data          16  system_stm32l4xx.o(.constdata)
    MSIRangeTable                            0x080074d0   Data          48  system_stm32l4xx.o(.constdata)
    APBPrescTable                            0x08007500   Data           8  system_stm32l4xx.o(.constdata)
    asc2_1206                                0x08007508   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800797c   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x08007f6c   Data        4560  lcd.o(.constdata)
    asc2_3216                                0x0800913c   Data       12160  lcd.o(.constdata)
    Region$$Table$$Base                      0x0800c0bc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800c0dc   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32l4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32l4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32l4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32l4xx.o(.data)
    POINT_COLOR                              0x20000010   Data           2  lcd.o(.data)
    BACK_COLOR                               0x20000012   Data           2  lcd.o(.data)
    __stdout                                 0x20000014   Data           4  stdout.o(.data)
    command                                  0x20000018   Data         128  main.o(.bss)
    hadc1                                    0x20000098   Data         100  adc.o(.bss)
    hdma_adc1                                0x200000fc   Data          72  adc.o(.bss)
    hdac1                                    0x20000144   Data          20  dac.o(.bss)
    hi2c1                                    0x20000158   Data          84  i2c.o(.bss)
    hi2c3                                    0x200001ac   Data          84  i2c.o(.bss)
    hlpuart1                                 0x20000200   Data         132  usart.o(.bss)
    huart1                                   0x20000284   Data         132  usart.o(.bss)
    huart2                                   0x20000308   Data         132  usart.o(.bss)
    huart3                                   0x2000038c   Data         132  usart.o(.bss)
    hdma_usart1_rx                           0x20000410   Data          72  usart.o(.bss)
    hdma_usart1_tx                           0x20000458   Data          72  usart.o(.bss)
    hqspi                                    0x200004a0   Data          76  quadspi.o(.bss)
    hsd1                                     0x200004ec   Data         132  sdmmc.o(.bss)
    hspi1                                    0x20000570   Data         100  spi.o(.bss)
    hspi2                                    0x200005d4   Data         100  spi.o(.bss)
    hspi3                                    0x20000638   Data         100  spi.o(.bss)
    htim2                                    0x2000069c   Data          76  tim.o(.bss)
    htim16                                   0x200006e8   Data          76  tim.o(.bss)
    __initial_sp                             0x20000fb8   Data           0  startup_stm32l431xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x0800018d

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000c0f4, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000c0dc, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x0000018c   Data   RO            3    RESET               startup_stm32l431xx.o
    0x0800018c   0x0800018c   0x00000000   Code   RO         7386  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         7668    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000190   0x08000190   0x00000004   Code   RO         7671    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000194   0x08000194   0x00000000   Code   RO         7673    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000194   0x08000194   0x00000000   Code   RO         7675    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000194   0x08000194   0x00000008   Code   RO         7676    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         7683    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001a0   0x080001a0   0x00000000   Code   RO         7678    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001a0   0x080001a0   0x00000000   Code   RO         7680    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001a0   0x080001a0   0x00000004   Code   RO         7669    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a4   0x080001a4   0x00000024   Code   RO            4    .text               startup_stm32l431xx.o
    0x080001c8   0x080001c8   0x00000068   Code   RO         7389    .text               mc_w.l(uldiv.o)
    0x08000230   0x08000230   0x00000024   Code   RO         7393    .text               mc_w.l(memseta.o)
    0x08000254   0x08000254   0x0000000e   Code   RO         7395    .text               mc_w.l(strlen.o)
    0x08000262   0x08000262   0x000000de   Code   RO         7660    .text               mf_w.l(ddiv.o)
    0x08000340   0x08000340   0x00000022   Code   RO         7662    .text               mf_w.l(dflti.o)
    0x08000362   0x08000362   0x00000026   Code   RO         7664    .text               mf_w.l(f2d.o)
    0x08000388   0x08000388   0x00000038   Code   RO         7666    .text               mf_w.l(d2f.o)
    0x080003c0   0x080003c0   0x0000002c   Code   RO         7685    .text               mc_w.l(uidiv.o)
    0x080003ec   0x080003ec   0x0000001e   Code   RO         7687    .text               mc_w.l(llshl.o)
    0x0800040a   0x0800040a   0x00000020   Code   RO         7689    .text               mc_w.l(llushr.o)
    0x0800042a   0x0800042a   0x00000000   Code   RO         7691    .text               mc_w.l(iusefp.o)
    0x0800042a   0x0800042a   0x0000006e   Code   RO         7692    .text               mf_w.l(fepilogue.o)
    0x08000498   0x08000498   0x000000ba   Code   RO         7694    .text               mf_w.l(depilogue.o)
    0x08000552   0x08000552   0x0000015e   Code   RO         7696    .text               mf_w.l(dadd.o)
    0x080006b0   0x080006b0   0x000000ea   Code   RO         7698    .text               mf_w.l(dmul.o)
    0x0800079a   0x0800079a   0x00000030   Code   RO         7700    .text               mf_w.l(dfixul.o)
    0x080007ca   0x080007ca   0x00000002   PAD
    0x080007cc   0x080007cc   0x00000030   Code   RO         7702    .text               mf_w.l(cdrcmple.o)
    0x080007fc   0x080007fc   0x00000024   Code   RO         7704    .text               mc_w.l(init.o)
    0x08000820   0x08000820   0x00000024   Code   RO         7706    .text               mc_w.l(llsshr.o)
    0x08000844   0x08000844   0x0000000c   Code   RO          713    i.ADC1_IRQHandler   stm32l4xx_it.o
    0x08000850   0x08000850   0x00000002   Code   RO         6714    i.BSP_SD_AbortCallback  bsp_driver_sd.o
    0x08000852   0x08000852   0x00000002   Code   RO         6723    i.BSP_SD_ReadCpltCallback  bsp_driver_sd.o
    0x08000854   0x08000854   0x00000002   Code   RO         6726    i.BSP_SD_WriteCpltCallback  bsp_driver_sd.o
    0x08000856   0x08000856   0x00000002   Code   RO          714    i.BusFault_Handler  stm32l4xx_it.o
    0x08000858   0x08000858   0x00000050   Code   RO         6499    i.Convert_BH1750    e53_ia1.o
    0x080008a8   0x080008a8   0x0000000c   Code   RO          715    i.DMA1_Channel1_IRQHandler  stm32l4xx_it.o
    0x080008b4   0x080008b4   0x0000000c   Code   RO          716    i.DMA1_Channel4_IRQHandler  stm32l4xx_it.o
    0x080008c0   0x080008c0   0x0000000c   Code   RO          717    i.DMA1_Channel5_IRQHandler  stm32l4xx_it.o
    0x080008cc   0x080008cc   0x00000002   Code   RO          718    i.DebugMon_Handler  stm32l4xx_it.o
    0x080008ce   0x080008ce   0x0000000e   Code   RO         6500    i.E53_IA1_Init      e53_ia1.o
    0x080008dc   0x080008dc   0x000000a0   Code   RO         6503    i.E53_IA1_Read_Data  e53_ia1.o
    0x0800097c   0x0800097c   0x00000006   Code   RO          719    i.EXTI2_IRQHandler  stm32l4xx_it.o
    0x08000982   0x08000982   0x00000006   Code   RO          720    i.EXTI3_IRQHandler  stm32l4xx_it.o
    0x08000988   0x08000988   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x0800098c   0x0800098c   0x00000002   Code   RO         1114    i.HAL_ADCEx_EndOfSamplingCallback  stm32l4xx_hal_adc_ex.o
    0x0800098e   0x0800098e   0x00000002   Code   RO         1117    i.HAL_ADCEx_InjectedConvCpltCallback  stm32l4xx_hal_adc_ex.o
    0x08000990   0x08000990   0x00000002   Code   RO         1120    i.HAL_ADCEx_InjectedQueueOverflowCallback  stm32l4xx_hal_adc_ex.o
    0x08000992   0x08000992   0x00000002   Code   RO         1125    i.HAL_ADCEx_LevelOutOfWindow2Callback  stm32l4xx_hal_adc_ex.o
    0x08000994   0x08000994   0x00000002   Code   RO         1126    i.HAL_ADCEx_LevelOutOfWindow3Callback  stm32l4xx_hal_adc_ex.o
    0x08000996   0x08000996   0x00000002   PAD
    0x08000998   0x08000998   0x000003f2   Code   RO          886    i.HAL_ADC_ConfigChannel  stm32l4xx_hal_adc.o
    0x08000d8a   0x08000d8a   0x00000002   Code   RO          887    i.HAL_ADC_ConvCpltCallback  stm32l4xx_hal_adc.o
    0x08000d8c   0x08000d8c   0x00000002   Code   RO          890    i.HAL_ADC_ErrorCallback  stm32l4xx_hal_adc.o
    0x08000d8e   0x08000d8e   0x00000202   Code   RO          894    i.HAL_ADC_IRQHandler  stm32l4xx_hal_adc.o
    0x08000f90   0x08000f90   0x000001b0   Code   RO          895    i.HAL_ADC_Init      stm32l4xx_hal_adc.o
    0x08001140   0x08001140   0x00000002   Code   RO          896    i.HAL_ADC_LevelOutOfWindowCallback  stm32l4xx_hal_adc.o
    0x08001142   0x08001142   0x00000002   PAD
    0x08001144   0x08001144   0x000000a4   Code   RO          287    i.HAL_ADC_MspInit   adc.o
    0x080011e8   0x080011e8   0x0000011e   Code   RO         3234    i.HAL_DAC_ConfigChannel  stm32l4xx_hal_dac.o
    0x08001306   0x08001306   0x00000028   Code   RO         3244    i.HAL_DAC_Init      stm32l4xx_hal_dac.o
    0x0800132e   0x0800132e   0x00000002   PAD
    0x08001330   0x08001330   0x0000005c   Code   RO          329    i.HAL_DAC_MspInit   dac.o
    0x0800138c   0x0800138c   0x0000004a   Code   RO         2606    i.HAL_DMA_Abort     stm32l4xx_hal_dma.o
    0x080013d6   0x080013d6   0x0000004e   Code   RO         2607    i.HAL_DMA_Abort_IT  stm32l4xx_hal_dma.o
    0x08001424   0x08001424   0x000000ba   Code   RO         2611    i.HAL_DMA_IRQHandler  stm32l4xx_hal_dma.o
    0x080014de   0x080014de   0x00000002   PAD
    0x080014e0   0x080014e0   0x000000c0   Code   RO         2612    i.HAL_DMA_Init      stm32l4xx_hal_dma.o
    0x080015a0   0x080015a0   0x00000024   Code   RO         1293    i.HAL_Delay         stm32l4xx_hal.o
    0x080015c4   0x080015c4   0x00000002   Code   RO         2540    i.HAL_GPIO_EXTI_Callback  stm32l4xx_hal_gpio.o
    0x080015c6   0x080015c6   0x00000002   PAD
    0x080015c8   0x080015c8   0x00000018   Code   RO         2541    i.HAL_GPIO_EXTI_IRQHandler  stm32l4xx_hal_gpio.o
    0x080015e0   0x080015e0   0x000001d6   Code   RO         2542    i.HAL_GPIO_Init     stm32l4xx_hal_gpio.o
    0x080017b6   0x080017b6   0x0000000a   Code   RO         2546    i.HAL_GPIO_WritePin  stm32l4xx_hal_gpio.o
    0x080017c0   0x080017c0   0x0000000c   Code   RO         1297    i.HAL_GetTick       stm32l4xx_hal.o
    0x080017cc   0x080017cc   0x00000056   Code   RO         1992    i.HAL_I2CEx_ConfigAnalogFilter  stm32l4xx_hal_i2c_ex.o
    0x08001822   0x08001822   0x00000052   Code   RO         1993    i.HAL_I2CEx_ConfigDigitalFilter  stm32l4xx_hal_i2c_ex.o
    0x08001874   0x08001874   0x000000b8   Code   RO         1531    i.HAL_I2C_Init      stm32l4xx_hal_i2c.o
    0x0800192c   0x0800192c   0x0000012a   Code   RO         1537    i.HAL_I2C_Master_Receive  stm32l4xx_hal_i2c.o
    0x08001a56   0x08001a56   0x00000002   PAD
    0x08001a58   0x08001a58   0x0000012a   Code   RO         1544    i.HAL_I2C_Master_Transmit  stm32l4xx_hal_i2c.o
    0x08001b82   0x08001b82   0x00000002   PAD
    0x08001b84   0x08001b84   0x000000f8   Code   RO          395    i.HAL_I2C_MspInit   i2c.o
    0x08001c7c   0x08001c7c   0x00000010   Code   RO         1303    i.HAL_IncTick       stm32l4xx_hal.o
    0x08001c8c   0x08001c8c   0x0000001e   Code   RO         1304    i.HAL_Init          stm32l4xx_hal.o
    0x08001caa   0x08001caa   0x00000002   PAD
    0x08001cac   0x08001cac   0x00000044   Code   RO         1305    i.HAL_InitTick      stm32l4xx_hal.o
    0x08001cf0   0x08001cf0   0x0000002c   Code   RO          855    i.HAL_MspInit       stm32l4xx_hal_msp.o
    0x08001d1c   0x08001d1c   0x0000001a   Code   RO         3029    i.HAL_NVIC_EnableIRQ  stm32l4xx_hal_cortex.o
    0x08001d36   0x08001d36   0x00000002   PAD
    0x08001d38   0x08001d38   0x00000040   Code   RO         3035    i.HAL_NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x08001d78   0x08001d78   0x00000024   Code   RO         3036    i.HAL_NVIC_SetPriorityGrouping  stm32l4xx_hal_cortex.o
    0x08001d9c   0x08001d9c   0x00000068   Code   RO         2833    i.HAL_PWREx_ControlVoltageScaling  stm32l4xx_hal_pwr_ex.o
    0x08001e04   0x08001e04   0x00000010   Code   RO         2856    i.HAL_PWREx_GetVoltageRange  stm32l4xx_hal_pwr_ex.o
    0x08001e14   0x08001e14   0x00000010   Code   RO         2725    i.HAL_PWR_EnableBkUpAccess  stm32l4xx_hal_pwr.o
    0x08001e24   0x08001e24   0x000000a8   Code   RO         3968    i.HAL_QSPI_Init     stm32l4xx_hal_qspi.o
    0x08001ecc   0x08001ecc   0x00000068   Code   RO          522    i.HAL_QSPI_MspInit  quadspi.o
    0x08001f34   0x08001f34   0x00000010   Code   RO         2169    i.HAL_RCCEx_EnableMSIPLLMode  stm32l4xx_hal_rcc_ex.o
    0x08001f44   0x08001f44   0x0000035a   Code   RO         2172    i.HAL_RCCEx_GetPeriphCLKFreq  stm32l4xx_hal_rcc_ex.o
    0x0800229e   0x0800229e   0x00000002   PAD
    0x080022a0   0x080022a0   0x000002ba   Code   RO         2175    i.HAL_RCCEx_PeriphCLKConfig  stm32l4xx_hal_rcc_ex.o
    0x0800255a   0x0800255a   0x00000002   PAD
    0x0800255c   0x0800255c   0x00000144   Code   RO         2047    i.HAL_RCC_ClockConfig  stm32l4xx_hal_rcc.o
    0x080026a0   0x080026a0   0x00000024   Code   RO         2053    i.HAL_RCC_GetPCLK1Freq  stm32l4xx_hal_rcc.o
    0x080026c4   0x080026c4   0x00000024   Code   RO         2054    i.HAL_RCC_GetPCLK2Freq  stm32l4xx_hal_rcc.o
    0x080026e8   0x080026e8   0x000000a4   Code   RO         2056    i.HAL_RCC_GetSysClockFreq  stm32l4xx_hal_rcc.o
    0x0800278c   0x0800278c   0x00000560   Code   RO         2059    i.HAL_RCC_OscConfig  stm32l4xx_hal_rcc.o
    0x08002cec   0x08002cec   0x00000008   Code   RO         6727    i.HAL_SD_AbortCallback  bsp_driver_sd.o
    0x08002cf4   0x08002cf4   0x00000002   Code   RO         4513    i.HAL_SD_ErrorCallback  stm32l4xx_hal_sd.o
    0x08002cf6   0x08002cf6   0x00000002   PAD
    0x08002cf8   0x08002cf8   0x00000198   Code   RO         4515    i.HAL_SD_GetCardCSD  stm32l4xx_hal_sd.o
    0x08002e90   0x08002e90   0x0000002c   Code   RO         4517    i.HAL_SD_GetCardState  stm32l4xx_hal_sd.o
    0x08002ebc   0x08002ebc   0x000001f8   Code   RO         4521    i.HAL_SD_IRQHandler  stm32l4xx_hal_sd.o
    0x080030b4   0x080030b4   0x00000036   Code   RO         4522    i.HAL_SD_Init       stm32l4xx_hal_sd.o
    0x080030ea   0x080030ea   0x00000002   PAD
    0x080030ec   0x080030ec   0x000000c6   Code   RO         4523    i.HAL_SD_InitCard   stm32l4xx_hal_sd.o
    0x080031b2   0x080031b2   0x00000002   PAD
    0x080031b4   0x080031b4   0x00000094   Code   RO          564    i.HAL_SD_MspInit    sdmmc.o
    0x08003248   0x08003248   0x00000008   Code   RO         6728    i.HAL_SD_RxCpltCallback  bsp_driver_sd.o
    0x08003250   0x08003250   0x00000008   Code   RO         6729    i.HAL_SD_TxCpltCallback  bsp_driver_sd.o
    0x08003258   0x08003258   0x000000e0   Code   RO         4761    i.HAL_SPI_Init      stm32l4xx_hal_spi.o
    0x08003338   0x08003338   0x0000014c   Code   RO          606    i.HAL_SPI_MspInit   spi.o
    0x08003484   0x08003484   0x0000018e   Code   RO         4769    i.HAL_SPI_Transmit  stm32l4xx_hal_spi.o
    0x08003612   0x08003612   0x00000028   Code   RO         3040    i.HAL_SYSTICK_Config  stm32l4xx_hal_cortex.o
    0x0800363a   0x0800363a   0x00000002   Code   RO         5817    i.HAL_TIMEx_Break2Callback  stm32l4xx_hal_tim_ex.o
    0x0800363c   0x0800363c   0x00000002   Code   RO         5818    i.HAL_TIMEx_BreakCallback  stm32l4xx_hal_tim_ex.o
    0x0800363e   0x0800363e   0x00000002   Code   RO         5819    i.HAL_TIMEx_CommutCallback  stm32l4xx_hal_tim_ex.o
    0x08003640   0x08003640   0x00000084   Code   RO         5821    i.HAL_TIMEx_ConfigBreakDeadTime  stm32l4xx_hal_tim_ex.o
    0x080036c4   0x080036c4   0x00000062   Code   RO         5103    i.HAL_TIM_Base_Init  stm32l4xx_hal_tim.o
    0x08003726   0x08003726   0x00000002   PAD
    0x08003728   0x08003728   0x0000008c   Code   RO          666    i.HAL_TIM_Base_MspInit  tim.o
    0x080037b4   0x080037b4   0x00000002   Code   RO         5137    i.HAL_TIM_IC_CaptureCallback  stm32l4xx_hal_tim.o
    0x080037b6   0x080037b6   0x00000184   Code   RO         5151    i.HAL_TIM_IRQHandler  stm32l4xx_hal_tim.o
    0x0800393a   0x0800393a   0x00000002   Code   RO         5154    i.HAL_TIM_OC_DelayElapsedCallback  stm32l4xx_hal_tim.o
    0x0800393c   0x0800393c   0x00000124   Code   RO         5175    i.HAL_TIM_PWM_ConfigChannel  stm32l4xx_hal_tim.o
    0x08003a60   0x08003a60   0x00000062   Code   RO         5178    i.HAL_TIM_PWM_Init  stm32l4xx_hal_tim.o
    0x08003ac2   0x08003ac2   0x00000002   Code   RO         5180    i.HAL_TIM_PWM_MspInit  stm32l4xx_hal_tim.o
    0x08003ac4   0x08003ac4   0x00000002   Code   RO         5181    i.HAL_TIM_PWM_PulseFinishedCallback  stm32l4xx_hal_tim.o
    0x08003ac6   0x08003ac6   0x00000002   Code   RO         5189    i.HAL_TIM_PeriodElapsedCallback  stm32l4xx_hal_tim.o
    0x08003ac8   0x08003ac8   0x00000002   Code   RO         5194    i.HAL_TIM_TriggerCallback  stm32l4xx_hal_tim.o
    0x08003aca   0x08003aca   0x00000050   Code   RO         3877    i.HAL_UARTEx_ReceiveToIdle_IT  stm32l4xx_hal_uart_ex.o
    0x08003b1a   0x08003b1a   0x00000002   PAD
    0x08003b1c   0x08003b1c   0x00000054   Code   RO           14    i.HAL_UARTEx_RxEventCallback  main.o
    0x08003b70   0x08003b70   0x00000002   Code   RO         3879    i.HAL_UARTEx_WakeupCallback  stm32l4xx_hal_uart_ex.o
    0x08003b72   0x08003b72   0x00000002   Code   RO         3502    i.HAL_UART_ErrorCallback  stm32l4xx_hal_uart.o
    0x08003b74   0x08003b74   0x00000294   Code   RO         3505    i.HAL_UART_IRQHandler  stm32l4xx_hal_uart.o
    0x08003e08   0x08003e08   0x00000066   Code   RO         3506    i.HAL_UART_Init     stm32l4xx_hal_uart.o
    0x08003e6e   0x08003e6e   0x00000002   PAD
    0x08003e70   0x08003e70   0x0000021a   Code   RO          443    i.HAL_UART_MspInit  usart.o
    0x0800408a   0x0800408a   0x00000002   Code   RO         3513    i.HAL_UART_RxCpltCallback  stm32l4xx_hal_uart.o
    0x0800408c   0x0800408c   0x000000b6   Code   RO         3515    i.HAL_UART_Transmit  stm32l4xx_hal_uart.o
    0x08004142   0x08004142   0x00000002   Code   RO         3518    i.HAL_UART_TxCpltCallback  stm32l4xx_hal_uart.o
    0x08004144   0x08004144   0x00000002   Code   RO          721    i.HardFault_Handler  stm32l4xx_it.o
    0x08004146   0x08004146   0x00000022   Code   RO         1578    i.I2C_Flush_TXDR    stm32l4xx_hal_i2c.o
    0x08004168   0x08004168   0x00000120   Code   RO         1586    i.I2C_IsErrorOccurred  stm32l4xx_hal_i2c.o
    0x08004288   0x08004288   0x00000030   Code   RO         1595    i.I2C_TransferConfig  stm32l4xx_hal_i2c.o
    0x080042b8   0x080042b8   0x00000058   Code   RO         1597    i.I2C_WaitOnFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x08004310   0x08004310   0x0000009c   Code   RO         1598    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x080043ac   0x080043ac   0x0000004c   Code   RO         1599    i.I2C_WaitOnSTOPFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x080043f8   0x080043f8   0x00000050   Code   RO         1600    i.I2C_WaitOnTXISFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x08004448   0x08004448   0x00000020   Code   RO         6504    i.Init_BH1750       e53_ia1.o
    0x08004468   0x08004468   0x00000024   Code   RO         6505    i.Init_SHT30        e53_ia1.o
    0x0800448c   0x0800448c   0x00000052   Code   RO         6160    i.LCD_Address_Set   lcd.o
    0x080044de   0x080044de   0x00000002   PAD
    0x080044e0   0x080044e0   0x00000058   Code   RO         6161    i.LCD_Clear         lcd.o
    0x08004538   0x08004538   0x000001cc   Code   RO         6170    i.LCD_Init          lcd.o
    0x08004704   0x08004704   0x00000004   Code   RO         6172    i.LCD_SPI_Send      lcd.o
    0x08004708   0x08004708   0x00000142   Code   RO         6173    i.LCD_ShowChar      lcd.o
    0x0800484a   0x0800484a   0x0000004c   Code   RO         6175    i.LCD_ShowString    lcd.o
    0x08004896   0x08004896   0x00000002   PAD
    0x08004898   0x08004898   0x0000001c   Code   RO         6178    i.LCD_Write_Cmd     lcd.o
    0x080048b4   0x080048b4   0x0000001c   Code   RO         6179    i.LCD_Write_Data    lcd.o
    0x080048d0   0x080048d0   0x00000028   Code   RO         6180    i.LCD_Write_HalfWord  lcd.o
    0x080048f8   0x080048f8   0x0000000c   Code   RO          908    i.LL_ADC_GetOffsetChannel  stm32l4xx_hal_adc.o
    0x08004904   0x08004904   0x00000008   Code   RO          909    i.LL_ADC_INJ_IsConversionOngoing  stm32l4xx_hal_adc.o
    0x0800490c   0x0800490c   0x00000008   Code   RO          910    i.LL_ADC_REG_IsConversionOngoing  stm32l4xx_hal_adc.o
    0x08004914   0x08004914   0x00000010   Code   RO          911    i.LL_ADC_REG_IsTriggerSourceSWStart  stm32l4xx_hal_adc.o
    0x08004924   0x08004924   0x0000001e   Code   RO          913    i.LL_ADC_SetChannelSamplingTime  stm32l4xx_hal_adc.o
    0x08004942   0x08004942   0x00000010   Code   RO          914    i.LL_ADC_SetOffsetState  stm32l4xx_hal_adc.o
    0x08004952   0x08004952   0x00000002   PAD
    0x08004954   0x08004954   0x0000000c   Code   RO          722    i.LPUART1_IRQHandler  stm32l4xx_it.o
    0x08004960   0x08004960   0x00000078   Code   RO          288    i.MX_ADC1_Init      adc.o
    0x080049d8   0x080049d8   0x00000048   Code   RO          330    i.MX_DAC1_Init      dac.o
    0x08004a20   0x08004a20   0x0000004c   Code   RO          370    i.MX_DMA_Init       dma.o
    0x08004a6c   0x08004a6c   0x00000120   Code   RO          262    i.MX_GPIO_Init      gpio.o
    0x08004b8c   0x08004b8c   0x00000058   Code   RO          396    i.MX_I2C1_Init      i2c.o
    0x08004be4   0x08004be4   0x00000058   Code   RO          397    i.MX_I2C3_Init      i2c.o
    0x08004c3c   0x08004c3c   0x0000003c   Code   RO          444    i.MX_LPUART1_UART_Init  usart.o
    0x08004c78   0x08004c78   0x00000040   Code   RO          523    i.MX_QUADSPI_Init   quadspi.o
    0x08004cb8   0x08004cb8   0x00000024   Code   RO          565    i.MX_SDMMC1_SD_Init  sdmmc.o
    0x08004cdc   0x08004cdc   0x0000004c   Code   RO          607    i.MX_SPI1_Init      spi.o
    0x08004d28   0x08004d28   0x00000050   Code   RO          608    i.MX_SPI2_Init      spi.o
    0x08004d78   0x08004d78   0x0000004c   Code   RO          609    i.MX_SPI3_Init      spi.o
    0x08004dc4   0x08004dc4   0x0000009c   Code   RO          667    i.MX_TIM16_Init     tim.o
    0x08004e60   0x08004e60   0x0000003c   Code   RO          445    i.MX_USART1_UART_Init  usart.o
    0x08004e9c   0x08004e9c   0x0000003c   Code   RO          446    i.MX_USART2_UART_Init  usart.o
    0x08004ed8   0x08004ed8   0x0000003c   Code   RO          447    i.MX_USART3_UART_Init  usart.o
    0x08004f14   0x08004f14   0x00000002   Code   RO          723    i.MemManage_Handler  stm32l4xx_it.o
    0x08004f16   0x08004f16   0x00000002   Code   RO          724    i.NMI_Handler       stm32l4xx_it.o
    0x08004f18   0x08004f18   0x00000002   Code   RO          725    i.PendSV_Handler    stm32l4xx_it.o
    0x08004f1a   0x08004f1a   0x00000046   Code   RO           15    i.PeriphCommonClock_Config  main.o
    0x08004f60   0x08004f60   0x00000048   Code   RO         3994    i.QSPI_WaitFlagStateUntilTimeout  stm32l4xx_hal_qspi.o
    0x08004fa8   0x08004fa8   0x000000a8   Code   RO         2178    i.RCCEx_GetSAIxPeriphCLKFreq  stm32l4xx_hal_rcc_ex.o
    0x08005050   0x08005050   0x0000012c   Code   RO         2179    i.RCCEx_PLLSAI1_Config  stm32l4xx_hal_rcc_ex.o
    0x0800517c   0x0800517c   0x0000007c   Code   RO         2060    i.RCC_SetFlashLatencyFromMSIRange  stm32l4xx_hal_rcc.o
    0x080051f8   0x080051f8   0x0000000c   Code   RO          726    i.SDMMC1_IRQHandler  stm32l4xx_it.o
    0x08005204   0x08005204   0x00000032   Code   RO         4215    i.SDMMC_CmdAppCommand  stm32l4xx_ll_sdmmc.o
    0x08005236   0x08005236   0x00000002   PAD
    0x08005238   0x08005238   0x00000034   Code   RO         4216    i.SDMMC_CmdAppOperCommand  stm32l4xx_ll_sdmmc.o
    0x0800526c   0x0800526c   0x00000032   Code   RO         4217    i.SDMMC_CmdBlockLength  stm32l4xx_ll_sdmmc.o
    0x0800529e   0x0800529e   0x00000002   PAD
    0x080052a0   0x080052a0   0x00000050   Code   RO         4222    i.SDMMC_CmdGoIdleState  stm32l4xx_ll_sdmmc.o
    0x080052f0   0x080052f0   0x00000030   Code   RO         4224    i.SDMMC_CmdOperCond  stm32l4xx_ll_sdmmc.o
    0x08005320   0x08005320   0x00000032   Code   RO         4229    i.SDMMC_CmdSelDesel  stm32l4xx_ll_sdmmc.o
    0x08005352   0x08005352   0x0000002c   Code   RO         4230    i.SDMMC_CmdSendCID  stm32l4xx_ll_sdmmc.o
    0x0800537e   0x0800537e   0x0000002c   Code   RO         4231    i.SDMMC_CmdSendCSD  stm32l4xx_ll_sdmmc.o
    0x080053aa   0x080053aa   0x00000032   Code   RO         4234    i.SDMMC_CmdSendStatus  stm32l4xx_ll_sdmmc.o
    0x080053dc   0x080053dc   0x00000030   Code   RO         4235    i.SDMMC_CmdSetRelAdd  stm32l4xx_ll_sdmmc.o
    0x0800540c   0x0800540c   0x00000034   Code   RO         4239    i.SDMMC_CmdStopTransfer  stm32l4xx_ll_sdmmc.o
    0x08005440   0x08005440   0x00000120   Code   RO         4244    i.SDMMC_GetCmdResp1  stm32l4xx_ll_sdmmc.o
    0x08005560   0x08005560   0x00000050   Code   RO         4245    i.SDMMC_GetCmdResp2  stm32l4xx_ll_sdmmc.o
    0x080055b0   0x080055b0   0x00000044   Code   RO         4246    i.SDMMC_GetCmdResp3  stm32l4xx_ll_sdmmc.o
    0x080055f4   0x080055f4   0x00000088   Code   RO         4247    i.SDMMC_GetCmdResp6  stm32l4xx_ll_sdmmc.o
    0x0800567c   0x0800567c   0x00000058   Code   RO         4248    i.SDMMC_GetCmdResp7  stm32l4xx_ll_sdmmc.o
    0x080056d4   0x080056d4   0x00000008   Code   RO         4252    i.SDMMC_GetPowerState  stm32l4xx_ll_sdmmc.o
    0x080056dc   0x080056dc   0x00000006   Code   RO         4253    i.SDMMC_GetResponse  stm32l4xx_ll_sdmmc.o
    0x080056e2   0x080056e2   0x00000022   Code   RO         4254    i.SDMMC_Init        stm32l4xx_ll_sdmmc.o
    0x08005704   0x08005704   0x00000010   Code   RO         4256    i.SDMMC_PowerState_ON  stm32l4xx_ll_sdmmc.o
    0x08005714   0x08005714   0x00000006   Code   RO         4257    i.SDMMC_ReadFIFO    stm32l4xx_ll_sdmmc.o
    0x0800571a   0x0800571a   0x00000020   Code   RO         4258    i.SDMMC_SendCommand  stm32l4xx_ll_sdmmc.o
    0x0800573a   0x0800573a   0x0000000a   Code   RO         4260    i.SDMMC_WriteFIFO   stm32l4xx_ll_sdmmc.o
    0x08005744   0x08005744   0x00000044   Code   RO         4536    i.SD_DMARxAbort     stm32l4xx_hal_sd.o
    0x08005788   0x08005788   0x00000044   Code   RO         4538    i.SD_DMATxAbort     stm32l4xx_hal_sd.o
    0x080057cc   0x080057cc   0x000000ee   Code   RO         4540    i.SD_InitCard       stm32l4xx_hal_sd.o
    0x080058ba   0x080058ba   0x00000002   PAD
    0x080058bc   0x080058bc   0x000000ba   Code   RO         4541    i.SD_PowerON        stm32l4xx_hal_sd.o
    0x08005976   0x08005976   0x00000002   PAD
    0x08005978   0x08005978   0x00000028   Code   RO         6507    i.SHT3x_CalcRH      e53_ia1.o
    0x080059a0   0x080059a0   0x00000034   Code   RO         6508    i.SHT3x_CalcTemperatureC  e53_ia1.o
    0x080059d4   0x080059d4   0x0000003a   Code   RO         6509    i.SHT3x_CheckCrc    e53_ia1.o
    0x08005a0e   0x08005a0e   0x00000002   PAD
    0x08005a10   0x08005a10   0x00000010   Code   RO          610    i.SPI2_WriteByte    spi.o
    0x08005a20   0x08005a20   0x00000048   Code   RO         4799    i.SPI_EndRxTxTransaction  stm32l4xx_hal_spi.o
    0x08005a68   0x08005a68   0x000000e6   Code   RO         4804    i.SPI_WaitFifoStateUntilTimeout  stm32l4xx_hal_spi.o
    0x08005b4e   0x08005b4e   0x00000002   PAD
    0x08005b50   0x08005b50   0x000000b8   Code   RO         4805    i.SPI_WaitFlagStateUntilTimeout  stm32l4xx_hal_spi.o
    0x08005c08   0x08005c08   0x00000002   Code   RO          727    i.SVC_Handler       stm32l4xx_it.o
    0x08005c0a   0x08005c0a   0x00000002   PAD
    0x08005c0c   0x08005c0c   0x00000020   Code   RO         6510    i.Start_BH1750      e53_ia1.o
    0x08005c2c   0x08005c2c   0x00000004   Code   RO          728    i.SysTick_Handler   stm32l4xx_it.o
    0x08005c30   0x08005c30   0x00000094   Code   RO           16    i.SystemClock_Config  main.o
    0x08005cc4   0x08005cc4   0x00000010   Code   RO         6103    i.SystemInit        system_stm32l4xx.o
    0x08005cd4   0x08005cd4   0x0000000c   Code   RO          729    i.TIM2_IRQHandler   stm32l4xx_it.o
    0x08005ce0   0x08005ce0   0x0000006c   Code   RO         5196    i.TIM_Base_SetConfig  stm32l4xx_hal_tim.o
    0x08005d4c   0x08005d4c   0x00000078   Code   RO         5209    i.TIM_OC1_SetConfig  stm32l4xx_hal_tim.o
    0x08005dc4   0x08005dc4   0x00000078   Code   RO         5210    i.TIM_OC2_SetConfig  stm32l4xx_hal_tim.o
    0x08005e3c   0x08005e3c   0x00000074   Code   RO         5211    i.TIM_OC3_SetConfig  stm32l4xx_hal_tim.o
    0x08005eb0   0x08005eb0   0x00000060   Code   RO         5212    i.TIM_OC4_SetConfig  stm32l4xx_hal_tim.o
    0x08005f10   0x08005f10   0x0000005c   Code   RO         5213    i.TIM_OC5_SetConfig  stm32l4xx_hal_tim.o
    0x08005f6c   0x08005f6c   0x0000005c   Code   RO         5214    i.TIM_OC6_SetConfig  stm32l4xx_hal_tim.o
    0x08005fc8   0x08005fc8   0x000000c8   Code   RO         3520    i.UART_AdvFeatureConfig  stm32l4xx_hal_uart.o
    0x08006090   0x08006090   0x0000005c   Code   RO         3521    i.UART_CheckIdleState  stm32l4xx_hal_uart.o
    0x080060ec   0x080060ec   0x00000014   Code   RO         3522    i.UART_DMAAbortOnError  stm32l4xx_hal_uart.o
    0x08006100   0x08006100   0x0000004a   Code   RO         3532    i.UART_EndRxTransfer  stm32l4xx_hal_uart.o
    0x0800614a   0x0800614a   0x0000009e   Code   RO         3534    i.UART_RxISR_16BIT  stm32l4xx_hal_uart.o
    0x080061e8   0x080061e8   0x0000009e   Code   RO         3535    i.UART_RxISR_8BIT   stm32l4xx_hal_uart.o
    0x08006286   0x08006286   0x00000002   PAD
    0x08006288   0x08006288   0x00000200   Code   RO         3536    i.UART_SetConfig    stm32l4xx_hal_uart.o
    0x08006488   0x08006488   0x000000b8   Code   RO         3538    i.UART_Start_Receive_IT  stm32l4xx_hal_uart.o
    0x08006540   0x08006540   0x000000c6   Code   RO         3541    i.UART_WaitOnFlagUntilTimeout  stm32l4xx_hal_uart.o
    0x08006606   0x08006606   0x00000002   PAD
    0x08006608   0x08006608   0x0000000c   Code   RO          730    i.USART1_IRQHandler  stm32l4xx_it.o
    0x08006614   0x08006614   0x0000000c   Code   RO          731    i.USART3_IRQHandler  stm32l4xx_it.o
    0x08006620   0x08006620   0x00000002   Code   RO          732    i.UsageFault_Handler  stm32l4xx_it.o
    0x08006622   0x08006622   0x00000002   PAD
    0x08006624   0x08006624   0x00000020   Code   RO         7632    i.__0printf         mc_w.l(printfa.o)
    0x08006644   0x08006644   0x00000028   Code   RO         7634    i.__0sprintf        mc_w.l(printfa.o)
    0x0800666c   0x0800666c   0x00000034   Code   RO         7637    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080066a0   0x080066a0   0x00000020   Code   RO         3042    i.__NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x080066c0   0x080066c0   0x0000000e   Code   RO         7710    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080066ce   0x080066ce   0x00000002   Code   RO         7711    i.__scatterload_null  mc_w.l(handlers.o)
    0x080066d0   0x080066d0   0x0000000e   Code   RO         7712    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080066de   0x080066de   0x00000002   PAD
    0x080066e0   0x080066e0   0x0000018a   Code   RO         7639    i._fp_digits        mc_w.l(printfa.o)
    0x0800686a   0x0800686a   0x00000002   PAD
    0x0800686c   0x0800686c   0x000006ba   Code   RO         7640    i._printf_core      mc_w.l(printfa.o)
    0x08006f26   0x08006f26   0x00000024   Code   RO         7641    i._printf_post_padding  mc_w.l(printfa.o)
    0x08006f4a   0x08006f4a   0x0000002e   Code   RO         7642    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08006f78   0x08006f78   0x00000016   Code   RO         7643    i._snputc           mc_w.l(printfa.o)
    0x08006f8e   0x08006f8e   0x0000000a   Code   RO         7644    i._sputc            mc_w.l(printfa.o)
    0x08006f98   0x08006f98   0x000001a8   Code   RO         7359    i.esp8266_init      esp8266.o
    0x08007140   0x08007140   0x00000094   Code   RO         7360    i.esp8266_publish   esp8266.o
    0x080071d4   0x080071d4   0x00000018   Code   RO          448    i.fputc             usart.o
    0x080071ec   0x080071ec   0x0000029c   Code   RO           18    i.main              main.o
    0x08007488   0x08007488   0x00000038   Code   RO          449    i.uart_print        usart.o
    0x080074c0   0x080074c0   0x00000040   Data   RO         6104    .constdata          system_stm32l4xx.o
    0x08007500   0x08007500   0x00000008   Data   RO         6105    .constdata          system_stm32l4xx.o
    0x08007508   0x08007508   0x00004bb4   Data   RO         6184    .constdata          lcd.o
    0x0800c0bc   0x0800c0bc   0x00000020   Data   RO         7708    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x0800c0f4, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800c0dc, Size: 0x00000fb8, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800c0dc   0x0000000c   Data   RW         1321    .data               stm32l4xx_hal.o
    0x2000000c   0x0800c0e8   0x00000004   Data   RW         6106    .data               system_stm32l4xx.o
    0x20000010   0x0800c0ec   0x00000004   Data   RW         6185    .data               lcd.o
    0x20000014   0x0800c0f0   0x00000004   Data   RW         7684    .data               mc_w.l(stdout.o)
    0x20000018        -       0x00000080   Zero   RW           19    .bss                main.o
    0x20000098        -       0x000000ac   Zero   RW          289    .bss                adc.o
    0x20000144        -       0x00000014   Zero   RW          331    .bss                dac.o
    0x20000158        -       0x000000a8   Zero   RW          398    .bss                i2c.o
    0x20000200        -       0x000002a0   Zero   RW          450    .bss                usart.o
    0x200004a0        -       0x0000004c   Zero   RW          524    .bss                quadspi.o
    0x200004ec        -       0x00000084   Zero   RW          566    .bss                sdmmc.o
    0x20000570        -       0x0000012c   Zero   RW          611    .bss                spi.o
    0x2000069c        -       0x00000098   Zero   RW          669    .bss                tim.o
    0x20000734        -       0x00000480   Zero   RW         6183    .bss                lcd.o
    0x20000bb4   0x0800c0f4   0x00000004   PAD
    0x20000bb8        -       0x00000400   Zero   RW            1    STACK               startup_stm32l431xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       284         32          0          0        172       2091   adc.o
        30          0          0          0          0       4002   bsp_driver_sd.o
       164         18          0          0         20       1973   dac.o
        76          4          0          0          0        934   dma.o
       504         68          0          0          0       7226   e53_ia1.o
       572        344          0          0          0       1401   esp8266.o
       288         12          0          0          0       1203   gpio.o
       424         40          0          0        168       2874   i2c.o
      1128         50      19380          4       1152       8278   lcd.o
       974        316          0          0        128     770478   main.o
       168         24          0          0         76       1997   quadspi.o
       184         28          0          0        132       1952   sdmmc.o
       580         58          0          0        300       4240   spi.o
        36          8        396          0       1024        888   startup_stm32l431xx.o
       162         24          0         12          0      13173   stm32l4xx_hal.o
      2052         76          0          0          0      62774   stm32l4xx_hal_adc.o
        10          0          0          0          0       3853   stm32l4xx_hal_adc_ex.o
       198         14          0          0          0      34299   stm32l4xx_hal_cortex.o
       326          0          0          0          0       2471   stm32l4xx_hal_dac.o
       530         14          0          0          0       3847   stm32l4xx_hal_dma.o
       506         36          0          0          0       3784   stm32l4xx_hal_gpio.o
      1550         40          0          0          0      12186   stm32l4xx_hal_i2c.o
       168          0          0          0          0       2127   stm32l4xx_hal_i2c_ex.o
        44          6          0          0          0        994   stm32l4xx_hal_msp.o
        16          4          0          0          0        611   stm32l4xx_hal_pwr.o
       120         20          0          0          0       1506   stm32l4xx_hal_pwr_ex.o
       240         10          0          0          0       2906   stm32l4xx_hal_qspi.o
      2060         98          0          0          0       7460   stm32l4xx_hal_rcc.o
      2040         76          0          0          0       6574   stm32l4xx_hal_rcc_ex.o
      1770         34          0          0          0      10221   stm32l4xx_hal_sd.o
      1108         10          0          0          0       6282   stm32l4xx_hal_spi.o
      1632        114          0          0          0      14614   stm32l4xx_hal_tim.o
       138          6          0          0          0       3396   stm32l4xx_hal_tim_ex.o
      2546         70          0          0          0      14856   stm32l4xx_hal_uart.o
        82          0          0          0          0       2150   stm32l4xx_hal_uart_ex.o
       140         54          0          0          0      10710   stm32l4xx_it.o
      1340         48          0          0          0      20122   stm32l4xx_ll_sdmmc.o
        16          4         72          4          0       1389   system_stm32l4xx.o
       296         24          0          0        152       2202   tim.o
       858         72          0          0        672       7951   usart.o

    ----------------------------------------------------------------------
     25420       <USER>      <GROUP>         20       4000    1061995   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        60          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2354        102          0          0          0        764   printfa.o
         0          0          0          4          0          0   stdout.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
       104          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       350          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       234          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      4072        <USER>          <GROUP>          4          0       2440   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2740        118          0          4          0       1384   mc_w.l
      1326          0          0          0          0       1056   mf_w.l

    ----------------------------------------------------------------------
      4072        <USER>          <GROUP>          4          0       2440   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     29492       1974      19880         24       4000    1042191   Grand Totals
     29492       1974      19880         24       4000    1042191   ELF Image Totals
     29492       1974      19880         24          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                49372 (  48.21kB)
    Total RW  Size (RW Data + ZI Data)              4024 (   3.93kB)
    Total ROM Size (Code + RO Data + RW Data)      49396 (  48.24kB)

==============================================================================

